# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_peppol
# 
# Translators:
# <PERSON><PERSON>, 2025
# Wil <PERSON>, 2025
# <PERSON> <<EMAIL>>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-14 20:47+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2025\n"
"Language-Team: Dutch (https://app.transifex.com/odoo/teams/41243/nl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nl\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/wizard/account_move_send_wizard.py:0
msgid " (Customer not on Peppol)"
msgstr " (<PERSON><PERSON> niet op Peppol)"

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/wizard/account_move_send_wizard.py:0
msgid " (Demo)"
msgstr " (Demo)"

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/wizard/account_move_send_wizard.py:0
msgid " (Test)"
msgstr " (Test)"

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/wizard/account_move_send_wizard.py:0
msgid " (no VAT)"
msgstr " (geen btw)"

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/wizard/account_move_send_wizard.py:0
msgid "%(peppol_label)s%(disable_reason)s%(peppol_proxy_mode)s"
msgstr "%(peppol_label)s%(disable_reason)s%(peppol_proxy_mode)s"

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/account_move_send.py:0
msgid "%s has requested electronic invoices reception on Peppol."
msgstr "%s heeft elektronische factuurontvangst aangevraagd op Peppol."

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.mail_notification_layout_with_responsible_signature_and_peppol
msgid ""
"<br/>\n"
"                                In Belgium, electronic invoicing is <u>mandatory as of January 2026</u> - don't wait to register."
msgstr ""
"<br/>\n"
"In België is elektronische facturatie <u>verplicht vanaf januari 2026</u> - wacht niet langer om je te registreren."

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.account_peppol_view_move_form
msgid ""
"<span class=\"mx-1\" invisible=\"'demo_' not in peppol_message_uuid\"> (Demo)</span>\n"
"                    <span class=\"text-muted mx-3\" invisible=\"peppol_move_state != 'to_send'\">\n"
"                        The invoice will be sent automatically via Peppol\n"
"                    </span>"
msgstr ""
"<span class=\"mx-1\" invisible=\"'demo_' not in peppol_message_uuid\"> (Demo)</span>\n"
"                    <span class=\"text-muted mx-3\" invisible=\"peppol_move_state != 'to_send'\">\n"
"                        De factuur wordt automatisch via Peppol verzonden\n"
"                    </span>"

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/account_edi_proxy_user.py:0
msgid ""
"A participant with these details has already been registered on the network."
" If you have previously registered to an alternative Peppol service, please "
"deregister from that service, or request a migration key before trying "
"again. "
msgstr ""
"Een deelnemer met deze gegevens is al geregistreerd op je netwerk. Als je al"
" geregistreerd bent bij een andere Peppol-dienst, meld je dan af bij die "
"dienst of vraag een migratiesleutel aan voordat je het opnieuw probeert. "

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/res_company.py:0
msgid "A purchase journal must be used to receive Peppol documents."
msgstr ""
"Een inkoopdagboek moet gebruikt worden om Peppol documenten te ontvangen."

#. module: account_peppol
#: model:ir.model,name:account_peppol.model_account_edi_proxy_client_user
msgid "Account EDI proxy user"
msgstr "Account EDI-proxygebruiker"

#. module: account_peppol
#: model:ir.model,name:account_peppol.model_account_move_send
msgid "Account Move Send"
msgstr "Boeking verzenden"

#. module: account_peppol
#: model:ir.model,name:account_peppol.model_account_move_send_batch_wizard
msgid "Account Move Send Batch Wizard"
msgstr "Rekening verplaatsen Verzend wizard"

#. module: account_peppol
#: model:ir.model,name:account_peppol.model_account_move_send_wizard
msgid "Account Move Send Wizard"
msgstr "Wizard rekening verplaatsen verzenden"

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_account_journal__is_peppol_journal
msgid "Account used for Peppol"
msgstr "Account gebruikt voor Peppol"

#. module: account_peppol
#. odoo-javascript
#: code:addons/account_peppol/static/src/components/peppol_info/peppol_info.js:0
msgid "Activate"
msgstr "Activeer"

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.res_config_settings_view_form
msgid "Activate Electronic Invoicing"
msgstr "Elektronische facturatie activeren"

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/wizard/peppol_registration.py:0
msgid "Activate Electronic Invoicing (via Peppol)"
msgstr "Elektronische facturatie activeren (via Peppol)"

#. module: account_peppol
#. odoo-javascript
#: code:addons/account_peppol/static/src/components/res_config_settings_buttons/res_config_settings_buttons.js:0
msgid "Activate Peppol"
msgstr "Peppol activeren"

#. module: account_peppol
#. odoo-javascript
#: code:addons/account_peppol/static/src/components/res_config_settings_buttons/res_config_settings_buttons.js:0
msgid "Activate Peppol (Demo)"
msgstr "Peppol activeren (Demo)"

#. module: account_peppol
#. odoo-javascript
#: code:addons/account_peppol/static/src/components/res_config_settings_buttons/res_config_settings_buttons.js:0
msgid "Activate Peppol (Test)"
msgstr "Peppol activeren (Test)"

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.peppol_registration_form
msgid "Allow incoming invoices"
msgstr "Inkomende facturen toestaan"

#. module: account_peppol
#. odoo-javascript
#: code:addons/account_peppol/static/src/components/res_config_settings_buttons/res_config_settings_buttons.xml:0
msgid "Allow reception"
msgstr "Ontvangst toestaan"

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.res_config_settings_view_form
msgid "Allow sending and receiving invoices through the PEPPOL network"
msgstr ""
"Sta het verzenden en ontvangen van facturen toe via het PEPPOL-netwerk"

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_res_partner__available_peppol_edi_formats
#: model:ir.model.fields,field_description:account_peppol.field_res_users__available_peppol_edi_formats
msgid "Available Peppol Edi Formats"
msgstr "Beschikbare Peppol Edi-formaten"

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_res_partner__available_peppol_sending_methods
#: model:ir.model.fields,field_description:account_peppol.field_res_users__available_peppol_sending_methods
msgid "Available Peppol Sending Methods"
msgstr "Beschikbare Peppol-verzendmethoden"

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.peppol_registration_form
msgid ""
"By clicking the button below I accept that Odoo may process my e-invoices."
msgstr ""
"Door op onderstaande knop te klikken, ga ik ermee akkoord dat Odoo mijn "
"e-facturen mag verwerken."

#. module: account_peppol
#: model:ir.model.fields.selection,name:account_peppol.selection__res_company__account_peppol_proxy_state__receiver
msgid "Can send and receive"
msgstr "Kan verzenden en ontvangen"

#. module: account_peppol
#: model:ir.model.fields.selection,name:account_peppol.selection__res_company__account_peppol_proxy_state__sender
msgid "Can send but not receive"
msgstr "Kan verzenden maar niet ontvangen"

#. module: account_peppol
#: model:ir.model.fields.selection,name:account_peppol.selection__res_company__account_peppol_proxy_state__smp_registration
msgid "Can send, pending registration to receive"
msgstr "Kan verzenden, in afwachting van registratie om te ontvangen"

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.peppol_service_configuration
msgid "Cancel"
msgstr "Annuleren"

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.account_peppol_view_move_form
msgid "Cancel PEPPOL"
msgstr "PEPPOL annuleren"

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/account_move.py:0
msgid "Cannot cancel an entry that has already been sent to PEPPOL"
msgstr "Kan geen boeking annuleren dat reeds naar PEPPOL werd verzonden"

#. module: account_peppol
#: model:ir.model.fields.selection,name:account_peppol.selection__res_partner__peppol_verification_state__not_valid_format
msgid "Cannot receive this format"
msgstr "Kan dit formaat niet ontvangen"

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/account_edi_proxy_user.py:0
#: code:addons/account_peppol/wizard/peppol_registration.py:0
msgid "Cannot register a user with a %s application"
msgstr "Kan een gebruiker met een %s aanvraag niet registreren"

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/account_move_send.py:0
msgid "Check Partner(s)"
msgstr "Controleer partner(s)"

#. module: account_peppol
#: model:ir.model.fields,help:account_peppol.field_peppol_registration__peppol_eas
#: model:ir.model.fields,help:account_peppol.field_res_company__peppol_eas
#: model:ir.model.fields,help:account_peppol.field_res_config_settings__account_peppol_eas
msgid ""
"Code used to identify the Endpoint for BIS Billing 3.0 and its derivatives.\n"
"             List available at https://docs.peppol.eu/poacc/billing/3.0/codelist/eas/"
msgstr ""
"Code die wordt gebruikt om het eindpunt voor BIS Billing 3.0 en zijn afgeleiden te identificeren.\n"
"             Lijst beschikbaar op https://docs.peppol.eu/poacc/billing/3.0/codelist/eas/"

#. module: account_peppol
#: model:ir.model,name:account_peppol.model_res_company
msgid "Companies"
msgstr "Bedrijven"

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_peppol_registration__company_id
msgid "Company"
msgstr "Bedrijf"

#. module: account_peppol
#: model:ir.model,name:account_peppol.model_res_config_settings
msgid "Config Settings"
msgstr "Configuratie-instellingen"

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.res_config_settings_view_form
msgid "Configure Peppol Services"
msgstr "Peppol-diensten instellen"

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.peppol_service_configuration
msgid "Confirm"
msgstr "Bevestigen"

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/account_edi_proxy_user.py:0
msgid "Connection error, please try again later."
msgstr "Verbindingsfout, probeer het later nog eens."

#. module: account_peppol
#: model:ir.model,name:account_peppol.model_res_partner
msgid "Contact"
msgstr "Contact"

#. module: account_peppol
#. odoo-javascript
#: code:addons/account_peppol/static/src/components/res_config_settings_buttons/res_config_settings_buttons.js:0
msgid "Contact details were updated."
msgstr "Contactgegevens zijn bijgewerkt."

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/res_config_settings.py:0
msgid "Contact email and mobile number are required."
msgstr "E-mailadres en telefoonnummer van contactpersoon zijn vereist."

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/wizard/peppol_registration.py:0
msgid "Contact email and phone number are required."
msgstr "E-mailadres en telefoonnummer van contactpersoon zijn vereist."

#. module: account_peppol
#. odoo-javascript
#: code:addons/account_peppol/static/src/components/peppol_info/peppol_info.xml:0
msgid "Create, send and receive e-invoices for free."
msgstr "Gratis e-facturen maken, versturen en ontvangen."

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_account_peppol_service__create_uid
#: model:ir.model.fields,field_description:account_peppol.field_account_peppol_service_wizard__create_uid
#: model:ir.model.fields,field_description:account_peppol.field_peppol_registration__create_uid
msgid "Created by"
msgstr "Aangemaakt door"

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_account_peppol_service__create_date
#: model:ir.model.fields,field_description:account_peppol.field_account_peppol_service_wizard__create_date
#: model:ir.model.fields,field_description:account_peppol.field_peppol_registration__create_date
msgid "Created on"
msgstr "Aangemaakt op"

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/account_move_send.py:0
msgid "Customer is on Peppol but did not enable receiving documents."
msgstr ""
"De klant zit op Peppol, maar heeft het ontvangen van documenten niet "
"ingeschakeld."

#. module: account_peppol
#: model:ir.model.fields.selection,name:account_peppol.selection__peppol_registration__edi_mode__demo
#: model:ir.model.fields.selection,name:account_peppol.selection__peppol_registration__edi_mode_constraint__demo
msgid "Demo"
msgstr "Demo"

#. module: account_peppol
#. odoo-javascript
#: code:addons/account_peppol/static/src/components/res_config_settings_buttons/res_config_settings_buttons.js:0
msgid "Discard"
msgstr "Negeren"

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_account_peppol_service__display_name
#: model:ir.model.fields,field_description:account_peppol.field_account_peppol_service_wizard__display_name
#: model:ir.model.fields,field_description:account_peppol.field_peppol_registration__display_name
msgid "Display Name"
msgstr "Schermnaam"

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_account_peppol_service__document_identifier
msgid "Document Identifier"
msgstr "Identificatie van documenten"

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_account_peppol_service__document_name
msgid "Document Name"
msgstr "Documentnaam"

#. module: account_peppol
#: model:ir.model.fields.selection,name:account_peppol.selection__account_move__peppol_move_state__done
msgid "Done"
msgstr "Gereed"

#. module: account_peppol
#. odoo-javascript
#: code:addons/account_peppol/static/src/components/peppol_info/peppol_info.xml:0
msgid "E-invoices will soon be mandatory in many countries"
msgstr "E-facturen worden binnenkort in veel landen verplicht"

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_peppol_registration__edi_mode
msgid "EDI mode"
msgstr "EDI-modus"

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_res_config_settings__account_peppol_edi_mode
msgid "EDI operating mode"
msgstr "EDI bedrijfsmodus"

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_account_peppol_service_wizard__edi_user_id
#: model:ir.model.fields,field_description:account_peppol.field_peppol_registration__edi_user_id
#: model:ir.model.fields,field_description:account_peppol.field_res_config_settings__account_peppol_edi_user
msgid "EDI user"
msgstr "EDI-gebruiker"

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/account_edi_proxy_user.py:0
msgid "EDI user should be of type Peppol"
msgstr "EDI-gebruiker moet van het type Peppol zijn"

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_res_config_settings__account_peppol_edi_identification
msgid "Edi Identification"
msgstr "EDI identificatie"

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_peppol_registration__edi_mode_constraint
msgid "Edi Mode Constraint"
msgstr "Edi-modusbeperking"

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.peppol_registration_form
msgid "Email"
msgstr "E-mail"

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_account_peppol_service__enabled
msgid "Enabled"
msgstr "Ingeschakeld"

#. module: account_peppol
#: model:ir.model.fields.selection,name:account_peppol.selection__account_move__peppol_move_state__error
msgid "Error"
msgstr "Foutmelding"

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/account_move_send.py:0
msgid "Errors occurred while creating the EDI document (format: %s):"
msgstr ""
"Er zijn fouten opgetreden bij het maken van het EDI-document (formaat: %s):"

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.account_journal_dashboard_kanban_view
msgid "Fetch Peppol invoice status"
msgstr "Haal de Peppol-factuurstatus op"

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.account_journal_dashboard_kanban_view
msgid "Fetch from Peppol"
msgstr "Ophalen bij Peppol"

#. module: account_peppol
#. odoo-javascript
#: code:addons/account_peppol/static/src/components/peppol_info/peppol_info.xml:0
msgid "Free on Odoo"
msgstr "Gratis op Odoo"

#. module: account_peppol
#. odoo-javascript
#: code:addons/account_peppol/static/src/components/peppol_info/peppol_info.xml:0
msgid "Fully automated"
msgstr "Volledig geautomatiseerd"

#. module: account_peppol
#. odoo-javascript
#: code:addons/account_peppol/static/src/components/peppol_info/peppol_info.js:0
msgid "Got it !"
msgstr "Begrepen!"

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.res_config_settings_view_form
msgid "I want to migrate my existing Peppol connection to Odoo (optional):"
msgstr ""
"Ik wil mijn bestaande Peppol-verbinding migreren naar Odoo (optioneel):"

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_account_peppol_service__id
#: model:ir.model.fields,field_description:account_peppol.field_account_peppol_service_wizard__id
#: model:ir.model.fields,field_description:account_peppol.field_peppol_registration__id
msgid "ID"
msgstr "ID"

#. module: account_peppol
#: model:ir.model.fields,help:account_peppol.field_peppol_registration__smp_registration
msgid ""
"If not check, you will only be able to send invoices but not receive them."
msgstr ""
"Als je dit niet aanvinkt, kun je alleen facturen versturen maar niet "
"ontvangen."

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/controllers/portal.py:0
msgid ""
"If you want to be invoiced by Peppol, your configuration must be valid."
msgstr ""
"Je configuratie dient geldig te zijn als je via Peppol wilt factureren."

#. module: account_peppol
#: model:ir.model.fields.selection,name:account_peppol.selection__res_company__account_peppol_proxy_state__in_verification
msgid "In verification"
msgstr "In verificatie"

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.res_config_settings_view_form
msgid "Incoming Invoices Journal"
msgstr "Dagboek binnenkomende facturen"

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_res_partner__invoice_sending_method
#: model:ir.model.fields,field_description:account_peppol.field_res_users__invoice_sending_method
msgid "Invoice sending"
msgstr "Factuur verzenden"

#. module: account_peppol
#: model:ir.model.fields,help:account_peppol.field_account_peppol_service_wizard__service_json
msgid ""
"JSON representation of peppol services as retrieved from the peppol server."
msgstr ""
"JSON-weergave van peppol-diensten zoals opgehaald van de peppol-server."

#. module: account_peppol
#: model:ir.model,name:account_peppol.model_account_journal
msgid "Journal"
msgstr "Dagboek"

#. module: account_peppol
#: model:ir.model,name:account_peppol.model_account_move
msgid "Journal Entry"
msgstr "Boeking"

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_account_peppol_service__write_uid
#: model:ir.model.fields,field_description:account_peppol.field_account_peppol_service_wizard__write_uid
#: model:ir.model.fields,field_description:account_peppol.field_peppol_registration__write_uid
msgid "Last Updated by"
msgstr "Laatst bijgewerkt door"

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_account_peppol_service__write_date
#: model:ir.model.fields,field_description:account_peppol.field_account_peppol_service_wizard__write_date
#: model:ir.model.fields,field_description:account_peppol.field_peppol_registration__write_date
msgid "Last Updated on"
msgstr "Laatste bijgewerkt op"

#. module: account_peppol
#: model:ir.model.fields.selection,name:account_peppol.selection__peppol_registration__edi_mode__prod
#: model:ir.model.fields.selection,name:account_peppol.selection__peppol_registration__edi_mode_constraint__prod
msgid "Live"
msgstr "Live"

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_peppol_registration__account_peppol_migration_key
#: model:ir.model.fields,field_description:account_peppol.field_res_company__account_peppol_migration_key
#: model:ir.model.fields,field_description:account_peppol.field_res_config_settings__account_peppol_migration_key
msgid "Migration Key"
msgstr "Migratiesleutel"

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_peppol_registration__phone_number
#: model:ir.model.fields,field_description:account_peppol.field_res_company__account_peppol_phone_number
#: model:ir.model.fields,field_description:account_peppol.field_res_config_settings__account_peppol_phone_number
msgid "Mobile number"
msgstr "Telefoonnummer"

#. module: account_peppol
#: model:ir.model.fields.selection,name:account_peppol.selection__res_partner__peppol_verification_state__not_valid
msgid "Not on Peppol"
msgstr "Niet op Peppol"

#. module: account_peppol
#: model:ir.model.fields.selection,name:account_peppol.selection__res_company__account_peppol_proxy_state__not_registered
msgid "Not registered"
msgstr "Niet geregistreerd"

#. module: account_peppol
#: model:ir.model.fields.selection,name:account_peppol.selection__res_partner__peppol_verification_state__not_verified
msgid "Not verified yet"
msgstr "Nog niet gecontroleerd"

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.mail_notification_layout_with_responsible_signature_and_peppol
msgid "Odoo"
msgstr "Odoo"

#. module: account_peppol
#. odoo-javascript
#: code:addons/account_peppol/static/src/components/peppol_info/peppol_info.xml:0
msgid "Odoo keeps you up to date with the new regulation."
msgstr "Odoo houdt je op de hoogte van de nieuwe regelgeving."

#. module: account_peppol
#: model:ir.model.fields.selection,name:account_peppol.selection__account_edi_proxy_client_user__proxy_type__peppol
msgid "PEPPOL"
msgstr "PEPPOL"

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_res_company__peppol_purchase_journal_id
#: model:ir.model.fields,field_description:account_peppol.field_res_config_settings__account_peppol_purchase_journal_id
msgid "PEPPOL Purchase Journal"
msgstr "PEPPOL Inkoopdagboek"

#. module: account_peppol
#. odoo-javascript
#: code:addons/account_peppol/static/src/components/peppol_info/peppol_info.xml:0
msgid ""
"PEPPOL allows for complete automation of sending and receiving e-invoices."
msgstr ""
"PEPPOL maakt het mogelijk om het verzenden en ontvangen van e-facturen "
"volledig te automatiseren."

#. module: account_peppol
#. odoo-javascript
#: code:addons/account_peppol/static/src/components/peppol_info/peppol_info.xml:0
msgid ""
"PEPPOL is the secure standard for e-invoices used in EU and across the "
"world."
msgstr ""
"PEPPOL is de veilige standaard voor e-facturen die in de EU en de rest van "
"de wereld wordt gebruikt."

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_account_bank_statement_line__peppol_message_uuid
#: model:ir.model.fields,field_description:account_peppol.field_account_move__peppol_message_uuid
msgid "PEPPOL message ID"
msgstr "PEPPOL ID bericht"

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_account_bank_statement_line__peppol_move_state
#: model:ir.model.fields,field_description:account_peppol.field_account_journal__account_peppol_proxy_state
#: model:ir.model.fields,field_description:account_peppol.field_account_move__peppol_move_state
#: model:ir.model.fields,field_description:account_peppol.field_peppol_registration__account_peppol_proxy_state
#: model:ir.model.fields,field_description:account_peppol.field_res_company__account_peppol_proxy_state
#: model:ir.model.fields,field_description:account_peppol.field_res_config_settings__account_peppol_proxy_state
msgid "PEPPOL status"
msgstr "Status PEPPOL"

#. module: account_peppol
#: model:ir.actions.server,name:account_peppol.ir_cron_peppol_get_new_documents_ir_actions_server
msgid "PEPPOL: retrieve new documents"
msgstr "PEPPOL: Nieuwe documenten ophalen"

#. module: account_peppol
#: model:ir.actions.server,name:account_peppol.ir_cron_peppol_get_message_status_ir_actions_server
msgid "PEPPOL: update message status"
msgstr "PEPPOL: Status bericht bijwerken"

#. module: account_peppol
#: model:ir.actions.server,name:account_peppol.ir_cron_peppol_get_participant_status_ir_actions_server
msgid "PEPPOL: update participant status"
msgstr "PEPPOL: Status deelnemer bijwerken"

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/wizard/account_move_send_wizard.py:0
msgid "Partner doesn't have a valid Peppol configuration."
msgstr "Partner heeft geen geldige Peppol-configuratie."

#. module: account_peppol
#: model:ir.model.fields.selection,name:account_peppol.selection__account_move__peppol_move_state__processing
msgid "Pending Reception"
msgstr "In afwachting van ontvangst"

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_peppol_registration__peppol_endpoint
#: model:ir.model.fields,field_description:account_peppol.field_res_company__peppol_endpoint
#: model:ir.model.fields,field_description:account_peppol.field_res_config_settings__account_peppol_endpoint
#: model_terms:ir.ui.view,arch_db:account_peppol.portal_my_details_fields
msgid "Peppol Endpoint"
msgstr "Eindpunt Peppol"

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.peppol_registration_form
msgid "Peppol ID"
msgstr "Peppol ID"

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.account_peppol_view_account_invoice_filter
msgid "Peppol Ready"
msgstr "Peppol klaar"

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/account_journal.py:0
msgid "Peppol Ready invoices"
msgstr "Peppol Ready-facturen"

#. module: account_peppol
#: model:ir.model,name:account_peppol.model_peppol_registration
msgid "Peppol Registration"
msgstr "Registratie Peppol"

#. module: account_peppol
#: model:ir.model,name:account_peppol.model_account_peppol_service
msgid "Peppol Service"
msgstr "Dienst Peppol"

#. module: account_peppol
#: model:ir.model,name:account_peppol.model_account_peppol_service_wizard
msgid "Peppol Services Wizard"
msgstr "Peppol Wizard"

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/account_edi_proxy_user.py:0
msgid "Peppol document (UUID: %(uuid)s) has been received successfully"
msgstr "Peppol-document (UUID: %(uuid)s) is succesvol ontvangen"

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.portal_my_details_fields
msgid "Peppol e-Address (EAS)"
msgstr "Peppol e-Adres (EAS)"

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_peppol_registration__peppol_eas
#: model:ir.model.fields,field_description:account_peppol.field_res_company__peppol_eas
#: model:ir.model.fields,field_description:account_peppol.field_res_config_settings__account_peppol_eas
msgid "Peppol e-address (EAS)"
msgstr "Peppol e-address (EAS)"

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_res_partner__peppol_verification_state
#: model:ir.model.fields,field_description:account_peppol.field_res_users__peppol_verification_state
msgid "Peppol endpoint verification"
msgstr "Peppol eindpuntverificatie"

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/account_edi_proxy_user.py:0
msgid "Peppol error: %s"
msgstr "Peppol foutmelding: %s"

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.account_journal_dashboard_kanban_view
msgid "Peppol ready invoices"
msgstr "Peppol-ready facturen"

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.account_peppol_view_account_invoice_filter
msgid "Peppol status"
msgstr "Peppol status"

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/account_edi_proxy_user.py:0
msgid "Peppol status update: %s"
msgstr "Update status Peppol: %s"

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_peppol_registration__peppol_warnings
msgid "Peppol warnings"
msgstr "Waarschuwingen Peppol"

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.peppol_registration_form
msgid "Phone"
msgstr "Telefoon"

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/res_company.py:0
msgid ""
"Please enter the mobile number in the correct international format.\n"
"For example: +***********, where +32 is the country code.\n"
"Currently, only European countries are supported."
msgstr ""
"Voer het telefoonnummer in het juiste internationale formaat in.\n"
"Bijvoorbeeld: +***********, waarbij +32 het landnummer is.\n"
"Momenteel worden alleen Europese landen ondersteund."

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/account_edi_proxy_user.py:0
msgid "Please fill in the EAS code and the Participant ID code."
msgstr "Vul de EAS-code en de deelnemer-ID-code in."

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/res_company.py:0
msgid "Please install the phonenumbers library."
msgstr "Installeer de phonenumbers bibliotheek."

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/account_move_send.py:0
msgid "Please verify partner configuration in partner settings."
msgstr "Controleer de configuratie in de partnerinstellingen."

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_peppol_registration__contact_email
#: model:ir.model.fields,field_description:account_peppol.field_res_company__account_peppol_contact_email
#: model:ir.model.fields,field_description:account_peppol.field_res_config_settings__account_peppol_contact_email
#: model_terms:ir.ui.view,arch_db:account_peppol.res_config_settings_view_form
msgid "Primary contact email"
msgstr "E-mailadres hoofdcontactpersoon"

#. module: account_peppol
#: model:ir.model.fields,help:account_peppol.field_peppol_registration__contact_email
#: model:ir.model.fields,help:account_peppol.field_res_company__account_peppol_contact_email
#: model:ir.model.fields,help:account_peppol.field_res_config_settings__account_peppol_contact_email
msgid "Primary contact email for Peppol-related communication"
msgstr "E-mailadres hoofdcontactpersoon voor Peppol gerelateerde communicatie"

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_account_edi_proxy_client_user__proxy_type
msgid "Proxy Type"
msgstr "Type proxy"

#. module: account_peppol
#: model:ir.model.fields.selection,name:account_peppol.selection__account_move__peppol_move_state__to_send
msgid "Queued"
msgstr "In wachtrij"

#. module: account_peppol
#: model:ir.model.fields.selection,name:account_peppol.selection__account_move__peppol_move_state__ready
msgid "Ready to send"
msgstr "Klaar om te verzenden"

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_peppol_registration__smp_registration
msgid "Register as a receiver"
msgstr "Als ontvanger inschrijven"

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/tools/demo_utils.py:0
msgid "Registered as a sender (demo)."
msgstr "Geregistreerd als verzender (demo)."

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/tools/demo_utils.py:0
msgid "Registered to receive documents via Peppol (demo)."
msgstr "Geregistreerd om documenten te ontvangen via Peppol (demo)."

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/res_config_settings.py:0
msgid "Registered to receive documents via Peppol."
msgstr "Geregistreerd om documenten te ontvangen via Peppol."

#. module: account_peppol
#: model:ir.model.fields.selection,name:account_peppol.selection__res_company__account_peppol_proxy_state__rejected
msgid "Rejected"
msgstr "Afgewezen"

#. module: account_peppol
#. odoo-javascript
#: code:addons/account_peppol/static/src/components/res_config_settings_buttons/res_config_settings_buttons.js:0
msgid "Remove from Peppol"
msgstr "Verwijderen uit Peppol"

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_account_edi_proxy_client_user__peppol_verification_code
#: model:ir.model.fields,field_description:account_peppol.field_peppol_registration__verification_code
msgid "SMS verification code"
msgstr "SMS verificatiecode"

#. module: account_peppol
#. odoo-javascript
#: code:addons/account_peppol/static/src/components/res_config_settings_buttons/res_config_settings_buttons.xml:0
msgid "Send again"
msgstr "Verstuur opnieuw"

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.peppol_registration_form
msgid "Send electronic invoices, and receive bills automatically via Peppol"
msgstr ""
"Stuur elektronische facturen en ontvang facturen automatisch via Peppol"

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_account_peppol_service_wizard__service_ids
msgid "Service"
msgstr "Dienst"

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_account_peppol_service_wizard__service_info
msgid "Service Info"
msgstr "Dienstinfo"

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_account_peppol_service_wizard__service_json
msgid "Service Json"
msgstr "Json-dienst"

#. module: account_peppol
#: model:ir.model.fields.selection,name:account_peppol.selection__account_move__peppol_move_state__skipped
msgid "Skipped"
msgstr "Overgeslagen"

#. module: account_peppol
#: model:ir.model.fields.selection,name:account_peppol.selection__peppol_registration__edi_mode__test
#: model:ir.model.fields.selection,name:account_peppol.selection__peppol_registration__edi_mode_constraint__test
msgid "Test"
msgstr "Test"

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.peppol_registration_form
msgid ""
"Test mode allows sending e-invoices through the test Peppol network.\n"
"                                    By clicking the button below I accept that Odoo may process my e-invoices."
msgstr ""
"Met de testmodus kun je e-facturen versturen via het test Peppol netwerk.\n"
"                                    Door op onderstaande knop te klikken accepteer ik dat Odoo mijn e-facturen mag verwerken."

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/controllers/portal.py:0
msgid "That country is not available for Peppol."
msgstr "Dat land is niet beschikbaar voor Peppol."

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/res_company.py:0
msgid "The Peppol endpoint identification number is not correct."
msgstr "Het identificatienummer van het Peppol eindpunt is niet correct."

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/account_edi_proxy_user.py:0
msgid "The Peppol service that is used is likely to be %s."
msgstr "De Peppol-service die wordt gebruikt, is waarschijnlijk %s."

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/account_move_send.py:0
msgid "The document has been sent to the Peppol Access Point for processing"
msgstr ""
"Het document werd verzonden naar het Peppol Access Point voor verwerking"

#. module: account_peppol
#. odoo-javascript
#: code:addons/account_peppol/static/src/components/peppol_info/peppol_info.xml:0
msgid "The e-invoicing network"
msgstr "Het e-factuurnetwerk"

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/wizard/peppol_registration.py:0
msgid ""
"The endpoint number might not be correct. Please check if you entered the "
"right identification number."
msgstr ""
"Het eindpuntnummer is mogelijk niet correct. Controleer of je het juiste "
"identificatienummer hebt ingevoerd."

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/wizard/service_wizard.py:0
msgid ""
"The following services are listed on your participant but cannot be "
"configured here. If you wish to configure them differently, please contact "
"support."
msgstr ""
"De volgende diensten zijn voor je deelnemer aangegeven, maar kunnen hier "
"niet worden geconfigureerd. Neem contact op met support als je ze anders "
"wilt configureren."

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/account_move_send.py:0
msgid "The partner is missing Peppol EAS and/or Endpoint identifier."
msgstr "Er ontbreekt een Peppol EAS en/of Endpoint-ID voor de partner."

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/tools/demo_utils.py:0
msgid ""
"The peppol status of the documents has been reset when switching from Demo "
"to Live."
msgstr ""
"De peppolstatus van de documenten is gereset bij het overschakelen van Demo "
"naar Live."

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.res_partner_form_account_peppol
msgid ""
"The recommended identification method for Belgium is your Company Registry "
"Number."
msgstr ""
"De aanbevolen identificatiemethode voor België is het ondernemingsnummer."

#. module: account_peppol
#: model:ir.model.fields,help:account_peppol.field_res_config_settings__account_peppol_edi_identification
msgid "The unique id that identifies this user, typically the vat"
msgstr "De unieke id die deze gebruiker identificeert, meestal het btw-nummer"

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/account_edi_proxy_user.py:0
msgid "The verification code is not correct"
msgstr "De verificatiecode is niet correct"

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/res_config_settings.py:0
msgid ""
"This feature is deprecated. Contact odoo support if you need a migration "
"key."
msgstr ""
"Deze functie is verouderd. Neem contact op met de Odoo-ondersteuning als je "
"een migratiesleutel nodig hebt."

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.mail_notification_layout_with_responsible_signature_and_peppol
msgid "This invoice has also been"
msgstr "Deze factuur is ook"

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/account_edi_proxy_user.py:0
msgid "This verification code has expired. Please request a new one."
msgstr "Deze verificatiecode is verlopen. Vraag een nieuwe code aan."

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.res_partner_form_account_peppol
msgid ""
"To generate complete electronic invoices, also set a country for this "
"partner."
msgstr ""
"Stel ook een land in voor deze partner om een volledige elektronische "
"factuur te genereren."

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/account_edi_proxy_user.py:0
msgid "Too many attempts to request an SMS code. Please try again later."
msgstr ""
"Te veel pogingen om een SMS-code aan te vragen. Probeer het later nog eens."

#. module: account_peppol
#: model:ir.model.fields,help:account_peppol.field_peppol_registration__peppol_endpoint
#: model:ir.model.fields,help:account_peppol.field_res_company__peppol_endpoint
#: model:ir.model.fields,help:account_peppol.field_res_config_settings__account_peppol_endpoint
msgid ""
"Unique identifier used by the BIS Billing 3.0 and its derivatives, also "
"known as 'Endpoint ID'."
msgstr ""
"Unieke identificatiecode gebruikt door BIS Billing 3.0 en zijn derivaten, "
"ook wel 'Endpoint ID' genoemd."

#. module: account_peppol
#. odoo-javascript
#: code:addons/account_peppol/static/src/components/res_config_settings_buttons/res_config_settings_buttons.xml:0
msgid "Update"
msgstr "Bijwerken"

#. module: account_peppol
#: model:ir.model.fields,help:account_peppol.field_peppol_registration__edi_mode_constraint
msgid ""
"Using the config params, this field specifies which edi modes may be "
"selected from the UI"
msgstr ""
"Met behulp van de configuratieparameters specificeert dit veld welke edi-"
"modi kunnen worden geselecteerd vanuit de gebruikersinterface"

#. module: account_peppol
#: model:ir.model.fields.selection,name:account_peppol.selection__res_partner__peppol_verification_state__valid
msgid "Valid"
msgstr "Geldig"

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.res_partner_form_account_peppol
msgid "Verify"
msgstr "Controleer"

#. module: account_peppol
#: model:ir.actions.server,name:account_peppol.partner_action_verify_peppol
msgid "Verify Peppol"
msgstr "Controleer Peppol"

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.res_partner_form_account_peppol
msgid "Verify partner's PEPPOL endpoint"
msgstr "Controleer het PEPPOL-eindpunt van de partner"

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/account_move_send.py:0
msgid "View Partner(s)"
msgstr "Bekijk partner(s)"

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/account_edi_proxy_user.py:0
msgid ""
"We could not find a user with this information on our server. Please check "
"your information."
msgstr ""
"We konden geen gebruiker vinden met deze gegevens op onze server. Controleer"
" je gegevens."

#. module: account_peppol
#. odoo-javascript
#: code:addons/account_peppol/static/src/components/peppol_info/peppol_info.xml:0
msgid "What is Peppol and why it's great ?"
msgstr "Wat is Peppol en waarom is het geweldig?"

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/account_move_send.py:0
msgid "Why should I use PEPPOL ?"
msgstr "Waarom zou ik PEPPOL gebruiken?"

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/account_move_send.py:0
msgid "Why should you use it ?"
msgstr "Waarom zou je het gebruiken?"

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_account_peppol_service__wizard_id
msgid "Wizard"
msgstr "Wizard"

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/tools/demo_utils.py:0
msgid "You can now receive demo vendor bills."
msgstr "Je kunt nu demo-leveranciersfacturen ontvangen."

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/wizard/peppol_registration.py:0
msgid "You can now send and receive electronic invoices via Peppol"
msgstr "Je kunt nu elektronische facturen verzenden en ontvangen via Peppol"

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/wizard/peppol_registration.py:0
msgid "You can now send electronic invoices via Peppol."
msgstr "Je kunt nu elektronische facturen versturen via Peppol."

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/tools/demo_utils.py:0
msgid "You can now send invoices in demo mode."
msgstr "Je kunt nu facturen in demomodus verzenden."

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/account_move_send.py:0
msgid "You can send this invoice electronically via Peppol."
msgstr "Je kunt deze factuur elektronisch verzenden via Peppol."

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.res_config_settings_view_form
msgid ""
"You registration has been rejected, the reason has been sent to you via email.\n"
"                            Please contact our support if you need further assistance."
msgstr ""
"Je registratie is afgewezen, de reden hiervoor is per e-mail naar je toegestuurd.\n"
"                            Neem contact op met onze supportafdeling als je meer hulp nodig hebt."

#. module: account_peppol
#. odoo-javascript
#: code:addons/account_peppol/static/src/components/res_config_settings_buttons/res_config_settings_buttons.js:0
msgid ""
"You will not be able to send or receive Peppol documents in Odoo anymore. "
"Are you sure you want to proceed?"
msgstr ""
"Je kunt geen Peppol-documenten meer verzenden of ontvangen in Odoo. Weet je "
"zeker dat je wilt doorgaan?"

#. module: account_peppol
#: model:ir.model.fields,help:account_peppol.field_peppol_registration__phone_number
#: model:ir.model.fields,help:account_peppol.field_res_company__account_peppol_phone_number
#: model:ir.model.fields,help:account_peppol.field_res_config_settings__account_peppol_phone_number
msgid "You will receive a verification code to this mobile number"
msgstr "Je zal een verificatiecode ontvangen op dit telefoonnummer"

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.res_config_settings_view_form
msgid "Your Peppol ID"
msgstr "Je Peppol ID"

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/wizard/peppol_registration.py:0
msgid ""
"Your Peppol registration will be activated soon. You can already send "
"invoices."
msgstr ""
"Je Peppol registratie wordt binnenkort geactiveerd. Je kunt nu al facturen "
"versturen."

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/wizard/peppol_registration.py:0
msgid ""
"Your company is already registered on another Access Point for receiving "
"invoices.We will register you as a sender only."
msgstr ""
"Je bedrijf is al geregistreerd op een ander Access Point voor het ontvangen "
"van facturen. We registreren je alleen als verzender."

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.peppol_registration_form
msgid "Your endpoint"
msgstr "Je eindpunt"

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.res_config_settings_view_form
msgid "Your migration key is:"
msgstr "Je migratiesleutel is:"

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/res_config_settings.py:0
msgid ""
"Your registration on Peppol network should be activated within a day. The "
"updated status will be visible in Settings."
msgstr ""
"Je registratie op Peppol wordt binnen een dag geactiveerd. De bijgewerkte "
"status zal zichtbaar zijn in de Instellingen."

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.res_config_settings_view_form
msgid "Your registration should be activated within a day."
msgstr "Je registratie moet binnen de dag geactiveerd zijn."

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/controllers/portal.py:0
#: model:ir.model.fields.selection,name:account_peppol.selection__res_partner__invoice_sending_method__peppol
msgid "by Peppol"
msgstr "met Peppol"

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.mail_notification_layout_with_responsible_signature_and_peppol
msgid "could not be sent via Peppol"
msgstr "kon niet via Peppol worden verzonden"

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.res_config_settings_view_form
msgid "invoices and credit notes."
msgstr "facturen en creditnota's."

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.mail_notification_layout_with_responsible_signature_and_peppol
msgid "sent via Peppol"
msgstr "verzonden via Peppol"

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.mail_notification_layout_with_responsible_signature_and_peppol
msgid "to send invoices, but this one"
msgstr "om facturen te versturen, maar deze"

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.mail_notification_layout_with_responsible_signature_and_peppol
msgid "uses"
msgstr "gebruikers"
