# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* auth_password_policy
# 
# Translators:
# <PERSON><PERSON>, 2024
# <PERSON>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# <PERSON><PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 08:40+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Noemi Pla, 2025\n"
"Language-Team: Catalan (https://app.transifex.com/odoo/teams/41243/ca/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ca\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: auth_password_policy
#: model:ir.model,name:auth_password_policy.model_res_config_settings
msgid "Config Settings"
msgstr "Paràmetres de configuració"

#. module: auth_password_policy
#: model:ir.model.fields,field_description:auth_password_policy.field_res_config_settings__minlength
msgid "Minimum Password Length"
msgstr "Longitud mínima de la contrasenya"

#. module: auth_password_policy
#: model:ir.model.fields,help:auth_password_policy.field_res_config_settings__minlength
msgid ""
"Minimum number of characters passwords must contain, set to 0 to disable."
msgstr ""
"El nombre mínim de caràcters que ha de contenir una contrasenya, deixar en 0"
" per deshabilitar. "

#. module: auth_password_policy
#. odoo-javascript
#: code:addons/auth_password_policy/static/src/password_field.js:0
msgid "Password"
msgstr "Contrasenya"

#. module: auth_password_policy
#. odoo-javascript
#: code:addons/auth_password_policy/static/src/password_meter.js:0
msgid ""
"Required: %s\n"
"\n"
"Hint: to increase password strength, increase length, use multiple words, and use non-letter characters."
msgstr ""
"Requerit: %s\n"
"\n"
"Consell: per a augmentar la intensitat de la contrasenya, augmentar la longitud, utilitzar diverses paraules i utilitzar caràcters que no siguin lletres."

#. module: auth_password_policy
#: model:ir.model,name:auth_password_policy.model_res_users
msgid "User"
msgstr "Usuari"

#. module: auth_password_policy
#. odoo-python
#: code:addons/auth_password_policy/models/res_users.py:0
msgid ""
"Your password must contain at least %(minimal_length)d characters and only "
"has %(current_count)d."
msgstr ""
"La teva contrasenya ha de tenir, com a mínim, %(minimal_length)d caràcters i"
" només en té %(current_count)d."

#. module: auth_password_policy
#. odoo-javascript
#: code:addons/auth_password_policy/static/src/password_policy.js:0
msgid "at least %s character classes"
msgstr "com a mínim %s classes de caràcters diferents"

#. module: auth_password_policy
#. odoo-javascript
#: code:addons/auth_password_policy/static/src/password_policy.js:0
msgid "at least %s characters"
msgstr "com a mínim %s caràcters"

#. module: auth_password_policy
#. odoo-javascript
#: code:addons/auth_password_policy/static/src/password_policy.js:0
msgid "at least %s words"
msgstr "com a mínim %s paraules"
