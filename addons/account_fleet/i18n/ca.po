# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_fleet
# 
# Translators:
# <PERSON><PERSON> <man<PERSON><PERSON>@outlook.com>, 2024
# <PERSON>, 2024
# <PERSON><PERSON>, 2024
# <PERSON>, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON>emi <PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 08:39+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON>emi Pla, 2025\n"
"Language-Team: Catalan (https://app.transifex.com/odoo/teams/41243/ca/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ca\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: account_fleet
#: model_terms:ir.ui.view,arch_db:account_fleet.fleet_vehicle_log_services_view_form
msgid ""
"<span class=\"o_stat_text text-success\" invisible=\"account_move_state != 'posted'\" title=\"Service's Bill\">Service's Bill</span>\n"
"                            <span class=\"o_stat_text text-warning\" invisible=\"account_move_state == 'posted'\" title=\"Service's Bill\">Service's Bill</span>"
msgstr ""
"<span class=\"o_stat_text text-success\" invisible=\"account_move_state != 'posted'\" title=\"Service's Bill\">Factura del servei</span>\n"
"                            <span class=\"o_stat_text text-warning\" invisible=\"account_move_state == 'posted'\" title=\"Service's Bill\">Factura del servei</span>"

#. module: account_fleet
#: model:ir.model.fields,field_description:account_fleet.field_fleet_vehicle__account_move_ids
msgid "Account Move"
msgstr "Moviment de compte"

#. module: account_fleet
#: model:ir.model.fields,field_description:account_fleet.field_fleet_vehicle_log_services__account_move_line_id
msgid "Account Move Line"
msgstr "Línia de moviment del compte"

#. module: account_fleet
#. odoo-python
#: code:addons/account_fleet/models/fleet_vehicle_log_services.py:0
msgid "Bill"
msgstr "Factura"

#. module: account_fleet
#: model_terms:ir.ui.view,arch_db:account_fleet.fleet_vehicle_view_form
msgid "Bills"
msgstr "Factures"

#. module: account_fleet
#: model:ir.model.fields,field_description:account_fleet.field_fleet_vehicle__bill_count
msgid "Bills Count"
msgstr "Nombre de factures"

#. module: account_fleet
#: model:ir.model.fields,field_description:account_fleet.field_fleet_vehicle_log_services__amount
msgid "Cost"
msgstr "Cost"

#. module: account_fleet
#: model:ir.model,name:account_fleet.model_account_automatic_entry_wizard
msgid "Create Automatic Entries"
msgstr "Crear assentaments automàtics"

#. module: account_fleet
#: model_terms:ir.ui.view,arch_db:account_fleet.account_move_view_tree
msgid "Creation Date"
msgstr "Data de creació"

#. module: account_fleet
#: model:ir.model,name:account_fleet.model_account_move
msgid "Journal Entry"
msgstr "Assentament comptable"

#. module: account_fleet
#: model:ir.model,name:account_fleet.model_account_move_line
msgid "Journal Item"
msgstr "Apunt comptable"

#. module: account_fleet
#: model:ir.model.fields,field_description:account_fleet.field_account_move_line__need_vehicle
msgid "Need Vehicle"
msgstr "Necessita vehicle"

#. module: account_fleet
#. odoo-python
#: code:addons/account_fleet/models/account_move.py:0
msgid "Service Vendor Bill: %s"
msgstr "Factura del proveïdor del servei: %s"

#. module: account_fleet
#: model:ir.model,name:account_fleet.model_fleet_vehicle_log_services
msgid "Services for vehicles"
msgstr "Serveis pels vehicles"

#. module: account_fleet
#: model:ir.model.fields,field_description:account_fleet.field_fleet_vehicle_log_services__account_move_state
msgid "Status"
msgstr "Estat"

#. module: account_fleet
#: model:ir.model,name:account_fleet.model_fleet_vehicle
#: model:ir.model.fields,field_description:account_fleet.field_account_move_line__vehicle_id
#: model:ir.model.fields,field_description:account_fleet.field_fleet_vehicle_log_services__vehicle_id
msgid "Vehicle"
msgstr "Vehicle"

#. module: account_fleet
#: model:fleet.service.type,name:account_fleet.data_fleet_service_type_vendor_bill
msgid "Vendor Bill"
msgstr "Factura de proveïdor"

#. module: account_fleet
#. odoo-python
#: code:addons/account_fleet/models/fleet_vehicle_log_services.py:0
msgid ""
"You cannot delete log services records because one or more of them were bill"
" created."
msgstr ""
"No pots eliminar els serveis registrats perquè alguns ja tenen una factura "
"associada."

#. module: account_fleet
#. odoo-python
#: code:addons/account_fleet/models/fleet_vehicle_log_services.py:0
msgid ""
"You cannot modify amount of services linked to an account move line. Do it "
"on the related accounting entry instead."
msgstr ""
"No pots modificar la quantitat de serveis vinculats a una línia de moviment "
"de compte. Fes això en l'assentament comptable amb el qual està relacionat."

#. module: account_fleet
#: model_terms:ir.ui.view,arch_db:account_fleet.fleet_vehicle_view_form
msgid "show the vendor bills for this vehicle"
msgstr "mostrar les factures del proveïdor d'aquest vehicle"
