# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* analytic
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON> <<EMAIL>>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-04-07 20:36+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2025\n"
"Language-Team: Arabic (https://app.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: analytic
#. odoo-python
#: code:addons/analytic/models/analytic_account.py:0
msgid "%s (copy)"
msgstr "%s (نسخة)"

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.account_analytic_plan_form_view
msgid "<span class=\"o_stat_text\">Analytic Accounts</span>"
msgstr "<span class=\"o_stat_text\">الحساب التحليلي</span> "

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_form
msgid "<span class=\"o_stat_text\">Gross Margin</span>"
msgstr "<span class=\"o_stat_text\">الهامش الإجمالي</span>"

#. module: analytic
#. odoo-python
#: code:addons/analytic/models/analytic_plan.py:0
msgid ""
"A 'Project' plan needs to exist and its id needs to be set as "
"`analytic.project_plan` in the system variables"
msgstr ""
"يجب أن تكون هناك خطة 'للمشروع' ويجب أن يتم تعيين المعرف الخاص بها كـ "
"`analytic.project_plan` في متغيرات النظام "

#. module: analytic
#. odoo-javascript
#: code:addons/analytic/static/src/components/analytic_distribution/analytic_distribution.js:0
msgid "Account field"
msgstr "حقل النظام "

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_plan__account_ids
msgid "Accounts"
msgstr "الحسابات"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__message_needaction
msgid "Action Needed"
msgstr "إجراء مطلوب"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__active
msgid "Active"
msgstr "نشط"

#. module: analytic
#: model:account.analytic.account,name:analytic.analytic_active_account
msgid "Active account"
msgstr "حساب نشط "

#. module: analytic
#. odoo-javascript
#: code:addons/analytic/static/src/components/analytic_distribution/analytic_distribution.xml:0
msgid "Add a Line"
msgstr "إضافة بند"

#. module: analytic
#: model_terms:ir.actions.act_window,help:analytic.action_account_analytic_account_form
#: model_terms:ir.actions.act_window,help:analytic.action_analytic_account_form
msgid "Add a new analytic account"
msgstr "إضافة حساب تحليلي جديد"

#. module: analytic
#: model:account.analytic.account,name:analytic.analytic_administratif
msgid "Administrative"
msgstr "إداري"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_plan__all_account_count
msgid "All Analytic Accounts Count"
msgstr "عدد كافة الحسابات التحليلية "

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__amount
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_line_form
msgid "Amount"
msgstr "مبلغ"

#. module: analytic
#. odoo-javascript
#: code:addons/analytic/static/src/components/analytic_distribution/analytic_distribution.js:0
msgid "Amount field"
msgstr "حقل المبلغ "

#. module: analytic
#. odoo-javascript
#: code:addons/analytic/static/src/components/analytic_distribution/analytic_distribution.xml:0
msgid "Analytic"
msgstr "تحليلي"

#. module: analytic
#: model:ir.model,name:analytic.model_account_analytic_account
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__name
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__auto_account_id
#: model:ir.model.fields,field_description:analytic.field_analytic_plan_fields_mixin__auto_account_id
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_form
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_search
msgid "Analytic Account"
msgstr "الحساب التحليلي"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_res_config_settings__group_analytic_accounting
#: model:res.groups,name:analytic.group_analytic_accounting
msgid "Analytic Accounting"
msgstr "المحاسبة التحليلية "

#. module: analytic
#: model:ir.actions.act_window,name:analytic.action_account_analytic_account_form
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_list
msgid "Analytic Accounts"
msgstr "الحسابات التحليلية"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_plan__account_count
msgid "Analytic Accounts Count"
msgstr "عدد الحسابات التحليلية "

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_distribution_model__analytic_distribution
#: model:ir.model.fields,field_description:analytic.field_analytic_mixin__analytic_distribution
msgid "Analytic Distribution"
msgstr "التوزيع التحليلي"

#. module: analytic
#. odoo-javascript
#: code:addons/analytic/static/src/components/analytic_distribution/analytic_distribution.js:0
#: model:ir.model,name:analytic.model_account_analytic_distribution_model
#: model_terms:ir.ui.view,arch_db:analytic.account_analytic_distribution_model_form_view
#: model_terms:ir.ui.view,arch_db:analytic.account_analytic_distribution_model_tree_view
msgid "Analytic Distribution Model"
msgstr "نموذج التوزيع التحليلي "

#. module: analytic
#: model:ir.actions.act_window,name:analytic.action_analytic_distribution_model
msgid "Analytic Distribution Models"
msgstr "نماذج التوزيع التحليلي "

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_line_form
msgid "Analytic Item"
msgstr "العنصر التحليلي "

#. module: analytic
#: model:ir.actions.act_window,name:analytic.account_analytic_line_action_entries
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_line_graph
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_line_pivot
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_line_tree
msgid "Analytic Items"
msgstr "العناصر التحليلية"

#. module: analytic
#: model:ir.model,name:analytic.model_account_analytic_line
msgid "Analytic Line"
msgstr "البند التحليلي"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__line_ids
msgid "Analytic Lines"
msgstr "البنود التحليلية"

#. module: analytic
#: model:ir.model,name:analytic.model_analytic_mixin
msgid "Analytic Mixin"
msgstr "مجموعة المخصصات (Mixin) التحليلية "

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_applicability__analytic_plan_id
msgid "Analytic Plan"
msgstr "الخطة التحليلية "

#. module: analytic
#: model:ir.model,name:analytic.model_analytic_plan_fields_mixin
msgid "Analytic Plan Fields"
msgstr "حقل الخطة التحليلية "

#. module: analytic
#: model:ir.model,name:analytic.model_account_analytic_applicability
msgid "Analytic Plan's Applicabilities"
msgstr "إمكانية تطبيق الخطة التحليلية "

#. module: analytic
#: model:ir.actions.act_window,name:analytic.account_analytic_plan_action
#: model:ir.model,name:analytic.model_account_analytic_plan
#: model_terms:ir.ui.view,arch_db:analytic.account_analytic_plan_form_view
#: model_terms:ir.ui.view,arch_db:analytic.account_analytic_plan_tree_view
msgid "Analytic Plans"
msgstr "الخطط التحليلية "

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_distribution_model__analytic_precision
#: model:ir.model.fields,field_description:analytic.field_analytic_mixin__analytic_precision
msgid "Analytic Precision"
msgstr "الدقة التحليلية "

#. module: analytic
#. odoo-python
#: code:addons/analytic/models/analytic_plan.py:0
msgid "Analytical Accounts"
msgstr "حسابات تحليلية"

#. module: analytic
#. odoo-python
#: code:addons/analytic/models/analytic_plan.py:0
msgid "Analytical Plans"
msgstr "الخطط التحليلية "

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_applicability__applicability
#: model:ir.model.fields,field_description:analytic.field_account_analytic_plan__applicability_ids
#: model_terms:ir.ui.view,arch_db:analytic.account_analytic_plan_form_view
msgid "Applicability"
msgstr "قابلية التطبيق "

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_form
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_search
msgid "Archived"
msgstr "مؤرشف"

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_search
msgid "Associated Partner"
msgstr "شريك مرتبط "

#. module: analytic
#: model:account.analytic.account,name:analytic.analytic_asustek
msgid "Asustek"
msgstr "Asustek"

#. module: analytic
#. odoo-python
#: code:addons/analytic/models/analytic_line.py:0
msgid "At least one analytic account must be set"
msgstr "يجب أن يتم تعيين حساب تحليلي واحد على الأقل "

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__message_attachment_count
msgid "Attachment Count"
msgstr "عدد المرفقات"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__balance
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_list
msgid "Balance"
msgstr "الرصيد"

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_kanban
msgid "Balance:"
msgstr "الرصيد:"

#. module: analytic
#. odoo-javascript
#: code:addons/analytic/static/src/components/analytic_distribution/analytic_distribution.js:0
msgid "Business domain"
msgstr "نطاق الأعمال "

#. module: analytic
#: model:account.analytic.account,name:analytic.analytic_partners_camp_to_camp
msgid "Camp to Camp"
msgstr "Camp to Camp"

#. module: analytic
#: model:account.analytic.account,name:analytic.analytic_integration_c2c
msgid "CampToCamp"
msgstr "CampToCamp"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__category
msgid "Category"
msgstr "الفئة "

#. module: analytic
#: model:ir.actions.act_window,name:analytic.action_analytic_account_form
msgid "Chart of Analytic Accounts"
msgstr "شجرة الحسابات التحليلية "

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_plan__children_count
msgid "Children Plans Count"
msgstr "عدد الخطط الفرعية "

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_plan__children_ids
msgid "Childrens"
msgstr "الفروع"

#. module: analytic
#: model_terms:ir.actions.act_window,help:analytic.account_analytic_plan_action
msgid "Click to add a new analytic account plan."
msgstr "اضغط لإضافة خطة حساب تحليلي جديدة. "

#. module: analytic
#. odoo-javascript
#: code:addons/analytic/static/src/components/analytic_distribution/analytic_distribution.xml:0
msgid "Close"
msgstr "إغلاق"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_plan__color
msgid "Color"
msgstr "اللون"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__color
msgid "Color Index"
msgstr "مؤشر اللون "

#. module: analytic
#: model:account.analytic.account,name:analytic.analytic_commercial_marketing
msgid "Commercial & Marketing"
msgstr "الإعلان والتسويق "

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__company_id
#: model:ir.model.fields,field_description:analytic.field_account_analytic_applicability__company_id
#: model:ir.model.fields,field_description:analytic.field_account_analytic_distribution_model__company_id
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__company_id
msgid "Company"
msgstr "الشركة "

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_plan__complete_name
msgid "Complete Name"
msgstr "الاسم الكامل"

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.account_analytic_distribution_model_form_view
msgid "Conditions to meet"
msgstr "الشروط لاستيفائها "

#. module: analytic
#: model:ir.model,name:analytic.model_res_config_settings
msgid "Config Settings"
msgstr "تهيئة الإعدادات "

#. module: analytic
#: model:ir.model.fields,help:analytic.field_account_analytic_line__product_uom_category_id
msgid ""
"Conversion between Units of Measure can only occur if they belong to the "
"same category. The conversion will be made based on the ratios."
msgstr ""
"لا يمكن التحويل بين وحدات القياس إلا إذا كانت تنتمي لنفس الفئة. سيتم إجراء "
"التحويل بناءً على النسب."

#. module: analytic
#: model_terms:ir.actions.act_window,help:analytic.account_analytic_line_action
#: model_terms:ir.actions.act_window,help:analytic.account_analytic_line_action_entries
msgid ""
"Costs will be created automatically when you register supplier\n"
"                invoices, expenses or timesheets."
msgstr ""
"سوف يتم إنشاء التكاليف تلقائيًا عند تسجيل الفواتير،\n"
"                 أو النفقات أو الجداول الزمنية للمورد. "

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__create_uid
#: model:ir.model.fields,field_description:analytic.field_account_analytic_applicability__create_uid
#: model:ir.model.fields,field_description:analytic.field_account_analytic_distribution_model__create_uid
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__create_uid
#: model:ir.model.fields,field_description:analytic.field_account_analytic_plan__create_uid
msgid "Created by"
msgstr "أنشئ بواسطة"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__create_date
#: model:ir.model.fields,field_description:analytic.field_account_analytic_applicability__create_date
#: model:ir.model.fields,field_description:analytic.field_account_analytic_distribution_model__create_date
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__create_date
#: model:ir.model.fields,field_description:analytic.field_account_analytic_plan__create_date
msgid "Created on"
msgstr "أنشئ في"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__credit
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_list
msgid "Credit"
msgstr "الدائن"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__currency_id
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__currency_id
msgid "Currency"
msgstr "العملة"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__partner_id
msgid "Customer"
msgstr "العميل"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__date
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_line_filter
msgid "Date"
msgstr "التاريخ"

#. module: analytic
#: model:ir.model.fields,help:analytic.field_account_analytic_account__active
msgid "Deactivate the account."
msgstr "تعطيل الحساب. "

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__debit
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_list
msgid "Debit"
msgstr "المدين"

#. module: analytic
#: model:account.analytic.account,name:analytic.analytic_agrolait
msgid "Deco Addict"
msgstr "Deco Addict"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_plan__default_applicability
msgid "Default Applicability"
msgstr "قابلية التطبيق الافتراضية "

#. module: analytic
#: model:account.analytic.account,name:analytic.analytic_deltapc
msgid "Delta PC"
msgstr "Delta PC"

#. module: analytic
#: model:account.analytic.plan,name:analytic.analytic_plan_departments
msgid "Departments"
msgstr "الأقسام"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__name
#: model:ir.model.fields,field_description:analytic.field_account_analytic_plan__description
msgid "Description"
msgstr "الوصف"

#. module: analytic
#: model:account.analytic.account,name:analytic.analytic_desertic_hispafuentes
msgid "Desertic - Hispafuentes"
msgstr "Desertic - Hispafuentes"

#. module: analytic
#. odoo-javascript
#: code:addons/analytic/static/src/components/analytic_distribution/analytic_distribution.js:0
msgid "Disable save"
msgstr "تعطيل الحفظ "

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__display_name
#: model:ir.model.fields,field_description:analytic.field_account_analytic_applicability__display_name
#: model:ir.model.fields,field_description:analytic.field_account_analytic_distribution_model__display_name
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__display_name
#: model:ir.model.fields,field_description:analytic.field_account_analytic_plan__display_name
msgid "Display Name"
msgstr "اسم العرض "

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_distribution_model__distribution_analytic_account_ids
#: model:ir.model.fields,field_description:analytic.field_analytic_mixin__distribution_analytic_account_ids
msgid "Distribution Analytic Account"
msgstr "حساب التوزيع التحليلي "

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.account_analytic_distribution_model_form_view
msgid "Distribution to apply"
msgstr "التوزيع لتطبيقه "

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_applicability__business_domain
msgid "Domain"
msgstr "النطاق"

#. module: analytic
#: model:account.analytic.account,name:analytic.analytic_rd_finance
msgid "Finance"
msgstr "المالية"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__message_follower_ids
msgid "Followers"
msgstr "المتابعين"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__message_partner_ids
msgid "Followers (Partners)"
msgstr "المتابعين (الشركاء) "

#. module: analytic
#. odoo-javascript
#: code:addons/analytic/static/src/components/analytic_distribution/analytic_distribution.js:0
msgid "Force applicability"
msgstr "فرض قابلية التطبيق "

#. module: analytic
#: model:ir.actions.act_window,name:analytic.account_analytic_line_action
msgid "Gross Margin"
msgstr "الهامش الإجمالي "

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_search
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_line_filter
msgid "Group By..."
msgstr "تجميع حسب.."

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__has_message
msgid "Has Message"
msgstr "يحتوي على رسالة "

#. module: analytic
#: model:account.analytic.account,name:analytic.analytic_rd_hr
msgid "Human Resources"
msgstr "الموارد البشرية"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__id
#: model:ir.model.fields,field_description:analytic.field_account_analytic_applicability__id
#: model:ir.model.fields,field_description:analytic.field_account_analytic_distribution_model__id
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__id
#: model:ir.model.fields,field_description:analytic.field_account_analytic_plan__id
msgid "ID"
msgstr "المُعرف"

#. module: analytic
#: model:ir.model.fields,help:analytic.field_account_analytic_account__message_needaction
msgid "If checked, new messages require your attention."
msgstr "إذا كان محددًا، فهناك رسائل جديدة عليك رؤيتها. "

#. module: analytic
#: model:ir.model.fields,help:analytic.field_account_analytic_account__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "إذا كان محددًا، فقد حدث خطأ في تسليم بعض الرسائل."

#. module: analytic
#: model_terms:ir.actions.act_window,help:analytic.account_analytic_line_action
#: model_terms:ir.actions.act_window,help:analytic.account_analytic_line_action_entries
msgid ""
"In Odoo, sales orders and projects are implemented using\n"
"                analytic accounts. You can track costs and revenues to analyse\n"
"                your margins easily."
msgstr ""
"في أودو، يتم تشغيل أوامر البيع والمشاريع باستخدام\n"
"                الحسابات التحليلية. يمكنك تتبع التكاليف والأرباح لتحليل\n"
"                هوامش الربح بسهولة."

#. module: analytic
#: model:account.analytic.plan,name:analytic.analytic_plan_internal
msgid "Internal"
msgstr "داخلي"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__message_is_follower
msgid "Is Follower"
msgstr "متابع"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__write_uid
#: model:ir.model.fields,field_description:analytic.field_account_analytic_applicability__write_uid
#: model:ir.model.fields,field_description:analytic.field_account_analytic_distribution_model__write_uid
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__write_uid
#: model:ir.model.fields,field_description:analytic.field_account_analytic_plan__write_uid
msgid "Last Updated by"
msgstr "آخر تحديث بواسطة"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__write_date
#: model:ir.model.fields,field_description:analytic.field_account_analytic_applicability__write_date
#: model:ir.model.fields,field_description:analytic.field_account_analytic_distribution_model__write_date
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__write_date
#: model:ir.model.fields,field_description:analytic.field_account_analytic_plan__write_date
msgid "Last Updated on"
msgstr "آخر تحديث في"

#. module: analytic
#: model:account.analytic.account,name:analytic.analytic_rd_legal
msgid "Legal"
msgstr "قانوني "

#. module: analytic
#: model:account.analytic.account,name:analytic.analytic_think_big_systems
msgid "Lumber Inc"
msgstr "Lumber Inc"

#. module: analytic
#: model:account.analytic.account,name:analytic.analytic_luminous_technologies
msgid "Luminous Technologies"
msgstr "Luminous Technologies"

#. module: analytic
#: model:ir.model.fields.selection,name:analytic.selection__account_analytic_applicability__applicability__mandatory
#: model:ir.model.fields.selection,name:analytic.selection__account_analytic_plan__default_applicability__mandatory
msgid "Mandatory"
msgstr "إلزامي "

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__message_has_error
msgid "Message Delivery error"
msgstr "خطأ في تسليم الرسائل"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__message_ids
msgid "Messages"
msgstr "الرسائل"

#. module: analytic
#: model:account.analytic.account,name:analytic.analytic_millennium_industries
msgid "Millennium Industries"
msgstr "Millennium Industries"

#. module: analytic
#: model:ir.model.fields.selection,name:analytic.selection__account_analytic_applicability__business_domain__general
msgid "Miscellaneous"
msgstr "متفرقات "

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_plan__name
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_list
msgid "Name"
msgstr "الاسم"

#. module: analytic
#: model:account.analytic.account,name:analytic.analytic_nebula
msgid "Nebula"
msgstr "Nebula"

#. module: analytic
#. odoo-javascript
#: code:addons/analytic/static/src/components/analytic_distribution/analytic_distribution.xml:0
msgid "New Model"
msgstr "نموذج جديد "

#. module: analytic
#: model_terms:ir.actions.act_window,help:analytic.account_analytic_line_action_entries
msgid "No activity yet"
msgstr "لا توجد أي أنشطة بعد "

#. module: analytic
#: model_terms:ir.actions.act_window,help:analytic.account_analytic_line_action
msgid "No activity yet on this account"
msgstr "لا توجد أي أنشطة بعد في هذا الحساب "

#. module: analytic
#. odoo-javascript
#: code:addons/analytic/static/src/components/analytic_distribution/analytic_distribution.xml:0
msgid "No analytic plans found"
msgstr "لم يتم العثور على أي خطط تحليلية "

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__message_needaction_counter
msgid "Number of Actions"
msgstr "عدد الإجراءات"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__message_has_error_counter
msgid "Number of errors"
msgstr "عدد الأخطاء "

#. module: analytic
#: model:ir.model.fields,help:analytic.field_account_analytic_account__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "عدد الرسائل التي تتطلب اتخاذ إجراء"

#. module: analytic
#: model:ir.model.fields,help:analytic.field_account_analytic_account__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "عدد الرسائل الحادث بها خطأ في التسليم"

#. module: analytic
#. odoo-python
#: code:addons/analytic/models/analytic_mixin.py:0
msgid "One or more lines require a 100% analytic distribution."
msgstr "هناك بند واحد أو أكثر بحاجة إلى التوزيع التحليلي بنسبة 100%. "

#. module: analytic
#: model:account.analytic.account,name:analytic.analytic_internal
msgid "Operating Costs"
msgstr "تكاليف التشغيل "

#. module: analytic
#. odoo-python
#: code:addons/analytic/models/analytic_mixin.py:0
msgid "Operation not supported"
msgstr "العملية غير مدعومة "

#. module: analytic
#: model:ir.model.fields.selection,name:analytic.selection__account_analytic_applicability__applicability__optional
#: model:ir.model.fields.selection,name:analytic.selection__account_analytic_plan__default_applicability__optional
msgid "Optional"
msgstr "اختياري "

#. module: analytic
#: model:ir.model.fields.selection,name:analytic.selection__account_analytic_line__category__other
msgid "Other"
msgstr "غير ذلك"

#. module: analytic
#: model:account.analytic.account,name:analytic.analytic_our_super_product
msgid "Our Super Product"
msgstr "منتجنا الخارق "

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_plan__parent_id
msgid "Parent"
msgstr "الأصل"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_plan__parent_path
msgid "Parent Path"
msgstr "المسار الرئيسي "

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_distribution_model__partner_id
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__partner_id
msgid "Partner"
msgstr "الشريك"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_distribution_model__partner_category_id
msgid "Partner Category"
msgstr "فئة الشريك "

#. module: analytic
#. odoo-javascript
#: code:addons/analytic/static/src/components/analytic_distribution/analytic_distribution.js:0
msgid "Percentage"
msgstr "النسبة"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__plan_id
msgid "Plan"
msgstr "الخطة "

#. module: analytic
#. odoo-javascript
#: code:addons/analytic/static/src/components/analytic_distribution/analytic_distribution.js:0
msgid "Product field"
msgstr "حقل المنتج "

#. module: analytic
#: model:account.analytic.account,name:analytic.analytic_rd_production
msgid "Production"
msgstr "الإنتاج"

#. module: analytic
#: model:account.analytic.plan,name:analytic.analytic_plan_projects
msgid "Project"
msgstr "المشروع"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__account_id
#: model:ir.model.fields,field_description:analytic.field_analytic_plan_fields_mixin__account_id
msgid "Project Account"
msgstr "حساب المشروع "

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__unit_amount
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_line_tree
msgid "Quantity"
msgstr "الكمية"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__code
msgid "Reference"
msgstr "الرقم المرجعي "

#. module: analytic
#: model:account.analytic.account,name:analytic.analytic_rd_department
msgid "Research & Development"
msgstr "البحث والتطوير "

#. module: analytic
#: model_terms:ir.actions.act_window,help:analytic.account_analytic_line_action
#: model_terms:ir.actions.act_window,help:analytic.account_analytic_line_action_entries
msgid ""
"Revenues will be created automatically when you create customer\n"
"                invoices. Customer invoices can be created based on sales orders\n"
"                (fixed price invoices), on timesheets (based on the work done) or\n"
"                on expenses (e.g. reinvoicing of travel costs)."
msgstr ""
"سوف يتم تسجيل الأرباح تلقائيًا عند إنشاء\n"
"                فواتير للعملاء. يمكن إنشاء فواتير العملاء بناءً على أوامر البيع\n"
"                (فواتير بأسعار ثابتة)، أو الجداول الزمنية (بناءً على العمل المنجز) أو\n"
"                النفقات (مثل إعادة إصدار فواتير لتكاليف السفر). "

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_plan__root_id
msgid "Root"
msgstr "مصدر"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__root_plan_id
msgid "Root Plan"
msgstr "الخطة الجذرية "

#. module: analytic
#. odoo-javascript
#: code:addons/analytic/static/src/components/analytic_distribution/analytic_distribution.xml:0
msgid "Save as new analytic distribution model"
msgstr "حفظ كنموذج توزيع تحليلي "

#. module: analytic
#: model:account.analytic.account,name:analytic.analytic_seagate_p2
msgid "Seagate P2"
msgstr "Seagate P2"

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_line_filter
msgid "Search Analytic Lines"
msgstr "البحث في البنود التحليلية "

#. module: analytic
#. odoo-python
#: code:addons/analytic/models/analytic_account.py:0
msgid "See them"
msgstr "ألقِ نظرة عليهم "

#. module: analytic
#: model:ir.model.fields,help:analytic.field_account_analytic_distribution_model__company_id
msgid ""
"Select a company for which the analytic distribution will be used (e.g. "
"create new customer invoice or Sales order if we select this company, it "
"will automatically take this as an analytic account)"
msgstr ""
"اختر شركة سيتم استخدام التوزيع التحليلي من أجلها (مثال: إنشاء فاتورة عميل "
"جديدة أو أمر بيع إذا قمنا بتحديد هذه الشركة، سيقوم تلقائياً بأخذه كحساب "
"تحليلي) "

#. module: analytic
#: model:ir.model.fields,help:analytic.field_account_analytic_distribution_model__partner_category_id
msgid ""
"Select a partner category for which the analytic distribution will be used "
"(e.g. create new customer invoice or Sales order if we select this partner, "
"it will automatically take this as an analytic account)"
msgstr ""
"اختر فئة شريك سيتم استخدام التوزيع التحليلي من أجلها (مثال: إنشاء فاتورة "
"عميل جديدة أو أمر بيع إذا قمنا بتحديد هذا الشريك، سيقوم تلقائياً بأخذه كحساب"
" تحليلي) "

#. module: analytic
#: model:ir.model.fields,help:analytic.field_account_analytic_distribution_model__partner_id
msgid ""
"Select a partner for which the analytic distribution will be used (e.g. "
"create new customer invoice or Sales order if we select this partner, it "
"will automatically take this as an analytic account)"
msgstr ""
"اختر شريكاً سيتم استخدام التوزيع التحليلي من أجله (مثال: إنشاء فاتورة عميل "
"جديدة أو أمر بيع إذا قمنا بتحديد هذا الشريك، سيقوم تلقائياً بأخذه كحساب "
"تحليلي) "

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_distribution_model__sequence
#: model:ir.model.fields,field_description:analytic.field_account_analytic_plan__sequence
msgid "Sequence"
msgstr "تسلسل "

#. module: analytic
#: model:account.analytic.account,name:analytic.analytic_spark
msgid "Spark Systems"
msgstr "أنظمة سبارك"

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.account_analytic_plan_form_view
msgid "Subplans"
msgstr "الخطط الفرعية "

#. module: analytic
#: model:account.analytic.account,name:analytic.analytic_absences
msgid "Time Off"
msgstr "الإجازات "

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_line_tree
msgid "Total"
msgstr "الإجمالي"

#. module: analytic
#: model:ir.model.fields.selection,name:analytic.selection__account_analytic_applicability__applicability__unavailable
#: model:ir.model.fields.selection,name:analytic.selection__account_analytic_plan__default_applicability__unavailable
msgid "Unavailable"
msgstr "غير متاح "

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__product_uom_id
msgid "Unit of Measure"
msgstr "وحدة القياس"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__product_uom_category_id
msgid "UoM Category"
msgstr "فئة وحدة القياس "

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__user_id
msgid "User"
msgstr "المستخدم"

#. module: analytic
#. odoo-python
#: code:addons/analytic/models/analytic_account.py:0
msgid ""
"Whoa there! Making this change would wipe out your current data. Let's avoid"
" that, shall we?"
msgstr "مهلاً! إجراء هذا التغيير سيمحو بياناتك الحالية. فلنتجنب ذلك، حسناً؟ "

#. module: analytic
#. odoo-python
#: code:addons/analytic/models/analytic_account.py:0
msgid ""
"You can't set a different company on your analytic account since there are "
"some analytic items linked to it."
msgstr ""
"لا يمكنك تعيين شركة أخرى على حسابك التحليلي نظراً لارتباط بعض العناصر "
"التحليلية به. "

#. module: analytic
#. odoo-python
#: code:addons/analytic/models/analytic_plan.py:0
msgid "You cannot add a parent to the base plan '%s'"
msgstr "لا يمكنك إضافة أصل لهذه الخطة الأساسية '%s' "

#. module: analytic
#. odoo-python
#: code:addons/analytic/models/analytic_distribution_model.py:0
msgid ""
"You defined a distribution with analytic account(s) belonging to a specific "
"company but a model shared between companies or with a different company"
msgstr ""
"قمت بتحديد توزيع باستخدام حساب (حسابات) تحليلية تنتمي إلى شركة محددة، ولكن "
"مع نموذج مشترك بين الشركات أو بشركة مختلفة "

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_form
msgid "e.g. Project XYZ"
msgstr "مثلًا: مشروع س ع ص"
