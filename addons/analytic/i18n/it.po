# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* analytic
# 
# Translators:
# Wil O<PERSON>o, 2024
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-04-07 20:36+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: Italian (https://app.transifex.com/odoo/teams/41243/it/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: it\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: analytic
#. odoo-python
#: code:addons/analytic/models/analytic_account.py:0
msgid "%s (copy)"
msgstr "%s (copia)"

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.account_analytic_plan_form_view
msgid "<span class=\"o_stat_text\">Analytic Accounts</span>"
msgstr "<span class=\"o_stat_text\">Conti analitici</span>"

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_form
msgid "<span class=\"o_stat_text\">Gross Margin</span>"
msgstr "<span class=\"o_stat_text\">Margine Lordo</span>"

#. module: analytic
#. odoo-python
#: code:addons/analytic/models/analytic_plan.py:0
msgid ""
"A 'Project' plan needs to exist and its id needs to be set as "
"`analytic.project_plan` in the system variables"
msgstr ""
"Un piano di 'Progetto' deve esistere e l'ID deve essere impostato su "
"`analytic.project_plan` nella variabili di sistema"

#. module: analytic
#. odoo-javascript
#: code:addons/analytic/static/src/components/analytic_distribution/analytic_distribution.js:0
msgid "Account field"
msgstr "Campo conto"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_plan__account_ids
msgid "Accounts"
msgstr "Conti"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__message_needaction
msgid "Action Needed"
msgstr "Azione richiesta"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__active
msgid "Active"
msgstr "Attivo"

#. module: analytic
#: model:account.analytic.account,name:analytic.analytic_active_account
msgid "Active account"
msgstr "Conto attivo"

#. module: analytic
#. odoo-javascript
#: code:addons/analytic/static/src/components/analytic_distribution/analytic_distribution.xml:0
msgid "Add a Line"
msgstr "Aggiungi una Riga"

#. module: analytic
#: model_terms:ir.actions.act_window,help:analytic.action_account_analytic_account_form
#: model_terms:ir.actions.act_window,help:analytic.action_analytic_account_form
msgid "Add a new analytic account"
msgstr "Aggiunge un nuovo conto analitico"

#. module: analytic
#: model:account.analytic.account,name:analytic.analytic_administratif
msgid "Administrative"
msgstr "Amministrativo"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_plan__all_account_count
msgid "All Analytic Accounts Count"
msgstr "Numero di tutti i conti analitici"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__amount
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_line_form
msgid "Amount"
msgstr "Importo"

#. module: analytic
#. odoo-javascript
#: code:addons/analytic/static/src/components/analytic_distribution/analytic_distribution.js:0
msgid "Amount field"
msgstr "Campo importo"

#. module: analytic
#. odoo-javascript
#: code:addons/analytic/static/src/components/analytic_distribution/analytic_distribution.xml:0
msgid "Analytic"
msgstr "Analitico"

#. module: analytic
#: model:ir.model,name:analytic.model_account_analytic_account
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__name
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__auto_account_id
#: model:ir.model.fields,field_description:analytic.field_analytic_plan_fields_mixin__auto_account_id
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_form
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_search
msgid "Analytic Account"
msgstr "Conto analitico"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_res_config_settings__group_analytic_accounting
#: model:res.groups,name:analytic.group_analytic_accounting
msgid "Analytic Accounting"
msgstr "Contabilità analitica"

#. module: analytic
#: model:ir.actions.act_window,name:analytic.action_account_analytic_account_form
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_list
msgid "Analytic Accounts"
msgstr "Conti analitici"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_plan__account_count
msgid "Analytic Accounts Count"
msgstr "Numero conti analitici"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_distribution_model__analytic_distribution
#: model:ir.model.fields,field_description:analytic.field_analytic_mixin__analytic_distribution
msgid "Analytic Distribution"
msgstr "Distribuzione analitica"

#. module: analytic
#. odoo-javascript
#: code:addons/analytic/static/src/components/analytic_distribution/analytic_distribution.js:0
#: model:ir.model,name:analytic.model_account_analytic_distribution_model
#: model_terms:ir.ui.view,arch_db:analytic.account_analytic_distribution_model_form_view
#: model_terms:ir.ui.view,arch_db:analytic.account_analytic_distribution_model_tree_view
msgid "Analytic Distribution Model"
msgstr "Modello di distribuzione analitico"

#. module: analytic
#: model:ir.actions.act_window,name:analytic.action_analytic_distribution_model
msgid "Analytic Distribution Models"
msgstr "Modelli di distribuzione analitici"

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_line_form
msgid "Analytic Item"
msgstr "Elemento analitico"

#. module: analytic
#: model:ir.actions.act_window,name:analytic.account_analytic_line_action_entries
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_line_graph
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_line_pivot
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_line_tree
msgid "Analytic Items"
msgstr "Movimenti analitici"

#. module: analytic
#: model:ir.model,name:analytic.model_account_analytic_line
msgid "Analytic Line"
msgstr "Riga analitica"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__line_ids
msgid "Analytic Lines"
msgstr "Righe analitiche"

#. module: analytic
#: model:ir.model,name:analytic.model_analytic_mixin
msgid "Analytic Mixin"
msgstr "Mixin analitico"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_applicability__analytic_plan_id
msgid "Analytic Plan"
msgstr "Piano analitico"

#. module: analytic
#: model:ir.model,name:analytic.model_analytic_plan_fields_mixin
msgid "Analytic Plan Fields"
msgstr "Campi piano analitico"

#. module: analytic
#: model:ir.model,name:analytic.model_account_analytic_applicability
msgid "Analytic Plan's Applicabilities"
msgstr "Applicabilità del piano analitico"

#. module: analytic
#: model:ir.actions.act_window,name:analytic.account_analytic_plan_action
#: model:ir.model,name:analytic.model_account_analytic_plan
#: model_terms:ir.ui.view,arch_db:analytic.account_analytic_plan_form_view
#: model_terms:ir.ui.view,arch_db:analytic.account_analytic_plan_tree_view
msgid "Analytic Plans"
msgstr "Piani analitici"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_distribution_model__analytic_precision
#: model:ir.model.fields,field_description:analytic.field_analytic_mixin__analytic_precision
msgid "Analytic Precision"
msgstr "Precisione analitica"

#. module: analytic
#. odoo-python
#: code:addons/analytic/models/analytic_plan.py:0
msgid "Analytical Accounts"
msgstr "Conti analitici"

#. module: analytic
#. odoo-python
#: code:addons/analytic/models/analytic_plan.py:0
msgid "Analytical Plans"
msgstr "Piani analitici"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_applicability__applicability
#: model:ir.model.fields,field_description:analytic.field_account_analytic_plan__applicability_ids
#: model_terms:ir.ui.view,arch_db:analytic.account_analytic_plan_form_view
msgid "Applicability"
msgstr "Applicabilità"

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_form
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_search
msgid "Archived"
msgstr "In archivio"

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_search
msgid "Associated Partner"
msgstr "Partner collegato"

#. module: analytic
#: model:account.analytic.account,name:analytic.analytic_asustek
msgid "Asustek"
msgstr "Asustek"

#. module: analytic
#. odoo-python
#: code:addons/analytic/models/analytic_line.py:0
msgid "At least one analytic account must be set"
msgstr "È necessario configurare almeno un conto analitico"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__message_attachment_count
msgid "Attachment Count"
msgstr "Numero allegati"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__balance
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_list
msgid "Balance"
msgstr "Saldo"

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_kanban
msgid "Balance:"
msgstr "Saldo:"

#. module: analytic
#. odoo-javascript
#: code:addons/analytic/static/src/components/analytic_distribution/analytic_distribution.js:0
msgid "Business domain"
msgstr "Ambito aziendale"

#. module: analytic
#: model:account.analytic.account,name:analytic.analytic_partners_camp_to_camp
msgid "Camp to Camp"
msgstr "Camp to Camp"

#. module: analytic
#: model:account.analytic.account,name:analytic.analytic_integration_c2c
msgid "CampToCamp"
msgstr "CampToCamp"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__category
msgid "Category"
msgstr "Categoria"

#. module: analytic
#: model:ir.actions.act_window,name:analytic.action_analytic_account_form
msgid "Chart of Analytic Accounts"
msgstr "Piano dei conti analitico"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_plan__children_count
msgid "Children Plans Count"
msgstr "Numero di piani figli"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_plan__children_ids
msgid "Childrens"
msgstr "Figli"

#. module: analytic
#: model_terms:ir.actions.act_window,help:analytic.account_analytic_plan_action
msgid "Click to add a new analytic account plan."
msgstr "Fai clic per aggiungere un nuobo piano analitico."

#. module: analytic
#. odoo-javascript
#: code:addons/analytic/static/src/components/analytic_distribution/analytic_distribution.xml:0
msgid "Close"
msgstr "Chiudi"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_plan__color
msgid "Color"
msgstr "Colore"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__color
msgid "Color Index"
msgstr "Indice colore"

#. module: analytic
#: model:account.analytic.account,name:analytic.analytic_commercial_marketing
msgid "Commercial & Marketing"
msgstr "Commerciale e marketing"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__company_id
#: model:ir.model.fields,field_description:analytic.field_account_analytic_applicability__company_id
#: model:ir.model.fields,field_description:analytic.field_account_analytic_distribution_model__company_id
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__company_id
msgid "Company"
msgstr "Azienda"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_plan__complete_name
msgid "Complete Name"
msgstr "Nome completo"

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.account_analytic_distribution_model_form_view
msgid "Conditions to meet"
msgstr "Condizioni da rispettare"

#. module: analytic
#: model:ir.model,name:analytic.model_res_config_settings
msgid "Config Settings"
msgstr "Impostazioni di configurazione"

#. module: analytic
#: model:ir.model.fields,help:analytic.field_account_analytic_line__product_uom_category_id
msgid ""
"Conversion between Units of Measure can only occur if they belong to the "
"same category. The conversion will be made based on the ratios."
msgstr ""
"Conversioni tra diverse unità di misura sono permesse solo se appartengono "
"alla stessa categoria. La conversione sarà effettuata utilizzando i rapporti"
" di conversione delle unità di misura."

#. module: analytic
#: model_terms:ir.actions.act_window,help:analytic.account_analytic_line_action
#: model_terms:ir.actions.act_window,help:analytic.account_analytic_line_action_entries
msgid ""
"Costs will be created automatically when you register supplier\n"
"                invoices, expenses or timesheets."
msgstr ""
"I costi verranno creati in modo automatico con la registrazione di\n"
"                fatture fornitore, note spese o fogli ore."

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__create_uid
#: model:ir.model.fields,field_description:analytic.field_account_analytic_applicability__create_uid
#: model:ir.model.fields,field_description:analytic.field_account_analytic_distribution_model__create_uid
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__create_uid
#: model:ir.model.fields,field_description:analytic.field_account_analytic_plan__create_uid
msgid "Created by"
msgstr "Creato da"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__create_date
#: model:ir.model.fields,field_description:analytic.field_account_analytic_applicability__create_date
#: model:ir.model.fields,field_description:analytic.field_account_analytic_distribution_model__create_date
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__create_date
#: model:ir.model.fields,field_description:analytic.field_account_analytic_plan__create_date
msgid "Created on"
msgstr "Data creazione"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__credit
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_list
msgid "Credit"
msgstr "Avere"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__currency_id
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__currency_id
msgid "Currency"
msgstr "Valuta"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__partner_id
msgid "Customer"
msgstr "Cliente"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__date
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_line_filter
msgid "Date"
msgstr "Data"

#. module: analytic
#: model:ir.model.fields,help:analytic.field_account_analytic_account__active
msgid "Deactivate the account."
msgstr "Disattiva il conto."

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__debit
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_list
msgid "Debit"
msgstr "Dare"

#. module: analytic
#: model:account.analytic.account,name:analytic.analytic_agrolait
msgid "Deco Addict"
msgstr "Deco Addict"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_plan__default_applicability
msgid "Default Applicability"
msgstr "Applicabilità predefinita"

#. module: analytic
#: model:account.analytic.account,name:analytic.analytic_deltapc
msgid "Delta PC"
msgstr "PC Delta"

#. module: analytic
#: model:account.analytic.plan,name:analytic.analytic_plan_departments
msgid "Departments"
msgstr "Reparti"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__name
#: model:ir.model.fields,field_description:analytic.field_account_analytic_plan__description
msgid "Description"
msgstr "Descrizione"

#. module: analytic
#: model:account.analytic.account,name:analytic.analytic_desertic_hispafuentes
msgid "Desertic - Hispafuentes"
msgstr "Desertico - Hispafuentes"

#. module: analytic
#. odoo-javascript
#: code:addons/analytic/static/src/components/analytic_distribution/analytic_distribution.js:0
msgid "Disable save"
msgstr "Disabilita salva"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__display_name
#: model:ir.model.fields,field_description:analytic.field_account_analytic_applicability__display_name
#: model:ir.model.fields,field_description:analytic.field_account_analytic_distribution_model__display_name
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__display_name
#: model:ir.model.fields,field_description:analytic.field_account_analytic_plan__display_name
msgid "Display Name"
msgstr "Nome visualizzato"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_distribution_model__distribution_analytic_account_ids
#: model:ir.model.fields,field_description:analytic.field_analytic_mixin__distribution_analytic_account_ids
msgid "Distribution Analytic Account"
msgstr "Distribuzione conto analitico"

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.account_analytic_distribution_model_form_view
msgid "Distribution to apply"
msgstr "Distribuzione da applicare"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_applicability__business_domain
msgid "Domain"
msgstr "Dominio"

#. module: analytic
#: model:account.analytic.account,name:analytic.analytic_rd_finance
msgid "Finance"
msgstr "Finanza"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__message_follower_ids
msgid "Followers"
msgstr "Seguito da"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__message_partner_ids
msgid "Followers (Partners)"
msgstr "Seguito da (partner)"

#. module: analytic
#. odoo-javascript
#: code:addons/analytic/static/src/components/analytic_distribution/analytic_distribution.js:0
msgid "Force applicability"
msgstr "Forza applicabilità"

#. module: analytic
#: model:ir.actions.act_window,name:analytic.account_analytic_line_action
msgid "Gross Margin"
msgstr "Margine lordo"

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_search
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_line_filter
msgid "Group By..."
msgstr "Raggruppa per..."

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__has_message
msgid "Has Message"
msgstr "Contiene messaggio"

#. module: analytic
#: model:account.analytic.account,name:analytic.analytic_rd_hr
msgid "Human Resources"
msgstr "Risorse umane"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__id
#: model:ir.model.fields,field_description:analytic.field_account_analytic_applicability__id
#: model:ir.model.fields,field_description:analytic.field_account_analytic_distribution_model__id
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__id
#: model:ir.model.fields,field_description:analytic.field_account_analytic_plan__id
msgid "ID"
msgstr "ID"

#. module: analytic
#: model:ir.model.fields,help:analytic.field_account_analytic_account__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Se selezionata, nuovi messaggi richiedono attenzione."

#. module: analytic
#: model:ir.model.fields,help:analytic.field_account_analytic_account__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "Se selezionata, alcuni messaggi presentano un errore di consegna."

#. module: analytic
#: model_terms:ir.actions.act_window,help:analytic.account_analytic_line_action
#: model_terms:ir.actions.act_window,help:analytic.account_analytic_line_action_entries
msgid ""
"In Odoo, sales orders and projects are implemented using\n"
"                analytic accounts. You can track costs and revenues to analyse\n"
"                your margins easily."
msgstr ""
"In Odoo, gli ordini di vendita e i progetti sono implementati usando\n"
"                conti analitici. È possibile tenere traccia dei costi e dei ricavi\n"
"                per analizzare facilmente i margini."

#. module: analytic
#: model:account.analytic.plan,name:analytic.analytic_plan_internal
msgid "Internal"
msgstr "Interno"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__message_is_follower
msgid "Is Follower"
msgstr "Sta seguendo"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__write_uid
#: model:ir.model.fields,field_description:analytic.field_account_analytic_applicability__write_uid
#: model:ir.model.fields,field_description:analytic.field_account_analytic_distribution_model__write_uid
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__write_uid
#: model:ir.model.fields,field_description:analytic.field_account_analytic_plan__write_uid
msgid "Last Updated by"
msgstr "Ultimo aggiornamento di"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__write_date
#: model:ir.model.fields,field_description:analytic.field_account_analytic_applicability__write_date
#: model:ir.model.fields,field_description:analytic.field_account_analytic_distribution_model__write_date
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__write_date
#: model:ir.model.fields,field_description:analytic.field_account_analytic_plan__write_date
msgid "Last Updated on"
msgstr "Ultimo aggiornamento il"

#. module: analytic
#: model:account.analytic.account,name:analytic.analytic_rd_legal
msgid "Legal"
msgstr "Legale"

#. module: analytic
#: model:account.analytic.account,name:analytic.analytic_think_big_systems
msgid "Lumber Inc"
msgstr "Lumber Inc"

#. module: analytic
#: model:account.analytic.account,name:analytic.analytic_luminous_technologies
msgid "Luminous Technologies"
msgstr "Tecnologie Luminous"

#. module: analytic
#: model:ir.model.fields.selection,name:analytic.selection__account_analytic_applicability__applicability__mandatory
#: model:ir.model.fields.selection,name:analytic.selection__account_analytic_plan__default_applicability__mandatory
msgid "Mandatory"
msgstr "Obbligatorio"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__message_has_error
msgid "Message Delivery error"
msgstr "Errore di consegna messaggio"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__message_ids
msgid "Messages"
msgstr "Messaggi"

#. module: analytic
#: model:account.analytic.account,name:analytic.analytic_millennium_industries
msgid "Millennium Industries"
msgstr "Industrie Millenium"

#. module: analytic
#: model:ir.model.fields.selection,name:analytic.selection__account_analytic_applicability__business_domain__general
msgid "Miscellaneous"
msgstr "Varie"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_plan__name
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_list
msgid "Name"
msgstr "Nome"

#. module: analytic
#: model:account.analytic.account,name:analytic.analytic_nebula
msgid "Nebula"
msgstr "Nebula"

#. module: analytic
#. odoo-javascript
#: code:addons/analytic/static/src/components/analytic_distribution/analytic_distribution.xml:0
msgid "New Model"
msgstr "Nuovo modello"

#. module: analytic
#: model_terms:ir.actions.act_window,help:analytic.account_analytic_line_action_entries
msgid "No activity yet"
msgstr "Ancora nessuna attività"

#. module: analytic
#: model_terms:ir.actions.act_window,help:analytic.account_analytic_line_action
msgid "No activity yet on this account"
msgstr "Ancora nessuna attività su questo conto"

#. module: analytic
#. odoo-javascript
#: code:addons/analytic/static/src/components/analytic_distribution/analytic_distribution.xml:0
msgid "No analytic plans found"
msgstr "Nessun piano analitico trovato"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__message_needaction_counter
msgid "Number of Actions"
msgstr "Numero di azioni"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__message_has_error_counter
msgid "Number of errors"
msgstr "Numero di errori"

#. module: analytic
#: model:ir.model.fields,help:analytic.field_account_analytic_account__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Numero di messaggi che richiedono un'azione"

#. module: analytic
#: model:ir.model.fields,help:analytic.field_account_analytic_account__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Numero di messaggi con errore di consegna"

#. module: analytic
#. odoo-python
#: code:addons/analytic/models/analytic_mixin.py:0
msgid "One or more lines require a 100% analytic distribution."
msgstr "Una o più righe richiedono una distribuzione analitica al 100%·"

#. module: analytic
#: model:account.analytic.account,name:analytic.analytic_internal
msgid "Operating Costs"
msgstr "Costi operativi"

#. module: analytic
#. odoo-python
#: code:addons/analytic/models/analytic_mixin.py:0
msgid "Operation not supported"
msgstr "Operazione non supportata"

#. module: analytic
#: model:ir.model.fields.selection,name:analytic.selection__account_analytic_applicability__applicability__optional
#: model:ir.model.fields.selection,name:analytic.selection__account_analytic_plan__default_applicability__optional
msgid "Optional"
msgstr "Opzionale"

#. module: analytic
#: model:ir.model.fields.selection,name:analytic.selection__account_analytic_line__category__other
msgid "Other"
msgstr "Altro"

#. module: analytic
#: model:account.analytic.account,name:analytic.analytic_our_super_product
msgid "Our Super Product"
msgstr "Il nostro super prodotto"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_plan__parent_id
msgid "Parent"
msgstr "Principale"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_plan__parent_path
msgid "Parent Path"
msgstr "Percorso padre"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_distribution_model__partner_id
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__partner_id
msgid "Partner"
msgstr "Partner"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_distribution_model__partner_category_id
msgid "Partner Category"
msgstr "Categoria partner"

#. module: analytic
#. odoo-javascript
#: code:addons/analytic/static/src/components/analytic_distribution/analytic_distribution.js:0
msgid "Percentage"
msgstr "Percentuale"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__plan_id
msgid "Plan"
msgstr "Pianificazione"

#. module: analytic
#. odoo-javascript
#: code:addons/analytic/static/src/components/analytic_distribution/analytic_distribution.js:0
msgid "Product field"
msgstr "Campo prodotto"

#. module: analytic
#: model:account.analytic.account,name:analytic.analytic_rd_production
msgid "Production"
msgstr "Produzione"

#. module: analytic
#: model:account.analytic.plan,name:analytic.analytic_plan_projects
msgid "Project"
msgstr "Progetto"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__account_id
#: model:ir.model.fields,field_description:analytic.field_analytic_plan_fields_mixin__account_id
msgid "Project Account"
msgstr "Conto progetto"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__unit_amount
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_line_tree
msgid "Quantity"
msgstr "Quantità"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__code
msgid "Reference"
msgstr "Riferimento"

#. module: analytic
#: model:account.analytic.account,name:analytic.analytic_rd_department
msgid "Research & Development"
msgstr "Ricerca & Sviluppo"

#. module: analytic
#: model_terms:ir.actions.act_window,help:analytic.account_analytic_line_action
#: model_terms:ir.actions.act_window,help:analytic.account_analytic_line_action_entries
msgid ""
"Revenues will be created automatically when you create customer\n"
"                invoices. Customer invoices can be created based on sales orders\n"
"                (fixed price invoices), on timesheets (based on the work done) or\n"
"                on expenses (e.g. reinvoicing of travel costs)."
msgstr ""
"I ricavi saranno creati in modo automatico con la creazione di fatture cliente.\n"
"                Le fatture cliente possono essere create in base agli ordini di vendita\n"
"                (fatturazione a prezzo fisso), ai fogli ore (in base al lavoro fatto) o alle\n"
"                spese (es. rifatturazione delle spese di viaggio)."

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_plan__root_id
msgid "Root"
msgstr "Radice"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__root_plan_id
msgid "Root Plan"
msgstr "Piano di base"

#. module: analytic
#. odoo-javascript
#: code:addons/analytic/static/src/components/analytic_distribution/analytic_distribution.xml:0
msgid "Save as new analytic distribution model"
msgstr "Salva come nuovo modello di distribuzione analitica"

#. module: analytic
#: model:account.analytic.account,name:analytic.analytic_seagate_p2
msgid "Seagate P2"
msgstr "Seagate P2"

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_line_filter
msgid "Search Analytic Lines"
msgstr "Ricerca registraioni analitiche"

#. module: analytic
#. odoo-python
#: code:addons/analytic/models/analytic_account.py:0
msgid "See them"
msgstr "Scoprili"

#. module: analytic
#: model:ir.model.fields,help:analytic.field_account_analytic_distribution_model__company_id
msgid ""
"Select a company for which the analytic distribution will be used (e.g. "
"create new customer invoice or Sales order if we select this company, it "
"will automatically take this as an analytic account)"
msgstr ""
"Seleziona un'azienda per la quale verrà utilizzata la distribuzione "
"analitica (ad es. crea una nuova fattura cliente oppure un ordine di vendita"
" se quest'azienda viene selezionata, ciò verrà considerato automaticamente "
"conto analitico)"

#. module: analytic
#: model:ir.model.fields,help:analytic.field_account_analytic_distribution_model__partner_category_id
msgid ""
"Select a partner category for which the analytic distribution will be used "
"(e.g. create new customer invoice or Sales order if we select this partner, "
"it will automatically take this as an analytic account)"
msgstr ""
"Seleziona una categoria partner per la quale verrà utilizzata la "
"distribuzione analitica (ad es. crea una nuova fattura cliente oppure un "
"ordine di vendita se questo partner viene selezionato, ciò verrà considerato"
" automaticamente conto analitico)"

#. module: analytic
#: model:ir.model.fields,help:analytic.field_account_analytic_distribution_model__partner_id
msgid ""
"Select a partner for which the analytic distribution will be used (e.g. "
"create new customer invoice or Sales order if we select this partner, it "
"will automatically take this as an analytic account)"
msgstr ""
"Seleziona un partner per il quale verrà utilizzata la distribuzione "
"analitica (ad es. crea una una fattura cliente oppure un ordine di vendita "
"se il partner viene selezionato, ciò verrà considerato automaticamente conto"
" analitico)"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_distribution_model__sequence
#: model:ir.model.fields,field_description:analytic.field_account_analytic_plan__sequence
msgid "Sequence"
msgstr "Sequenza"

#. module: analytic
#: model:account.analytic.account,name:analytic.analytic_spark
msgid "Spark Systems"
msgstr "Spark Systems"

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.account_analytic_plan_form_view
msgid "Subplans"
msgstr "Sottopiani"

#. module: analytic
#: model:account.analytic.account,name:analytic.analytic_absences
msgid "Time Off"
msgstr "Ferie"

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_line_tree
msgid "Total"
msgstr "Totale"

#. module: analytic
#: model:ir.model.fields.selection,name:analytic.selection__account_analytic_applicability__applicability__unavailable
#: model:ir.model.fields.selection,name:analytic.selection__account_analytic_plan__default_applicability__unavailable
msgid "Unavailable"
msgstr "Non disponibile"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__product_uom_id
msgid "Unit of Measure"
msgstr "Unità di misura"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__product_uom_category_id
msgid "UoM Category"
msgstr "Categoria unità di misura"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__user_id
msgid "User"
msgstr "Utente"

#. module: analytic
#. odoo-python
#: code:addons/analytic/models/analytic_account.py:0
msgid ""
"Whoa there! Making this change would wipe out your current data. Let's avoid"
" that, shall we?"
msgstr ""
"Ei tu! Se effettui questa modifica i tuoi dati attuali verranno cancellati. "
"Non sarebbe il caso di evitarlo?"

#. module: analytic
#. odoo-python
#: code:addons/analytic/models/analytic_account.py:0
msgid ""
"You can't set a different company on your analytic account since there are "
"some analytic items linked to it."
msgstr ""
"Non è possibile configurare un'azienda diversa nel tuo conto analitico "
"perché ci sono alcuni elementi analitici legati ad essa."

#. module: analytic
#. odoo-python
#: code:addons/analytic/models/analytic_plan.py:0
msgid "You cannot add a parent to the base plan '%s'"
msgstr "Impossibile aggiungere al piano base '%s' un padre"

#. module: analytic
#. odoo-python
#: code:addons/analytic/models/analytic_distribution_model.py:0
msgid ""
"You defined a distribution with analytic account(s) belonging to a specific "
"company but a model shared between companies or with a different company"
msgstr ""
"È stata indicata una distribuzione con uno o più conti analitici che "
"appartengono ad un'azienda specifica ma un modello condiviso tra più aziende"
" o con un'azienda diversa"

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_form
msgid "e.g. Project XYZ"
msgstr "es. progetto XYZ"
