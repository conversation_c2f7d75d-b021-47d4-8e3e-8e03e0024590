# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* auth_signup
# 
# Translators:
# Wil Odoo, 2025
# <PERSON><PERSON> <alina.lis<PERSON><PERSON>@erp.co.ua>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-03-19 20:36+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2025\n"
"Language-Team: Ukrainian (https://app.transifex.com/odoo/teams/41243/uk/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: uk\n"
"Plural-Forms: nplurals=4; plural=(n % 1 == 0 && n % 10 == 1 && n % 100 != 11 ? 0 : n % 1 == 0 && n % 10 >= 2 && n % 10 <= 4 && (n % 100 < 12 || n % 100 > 14) ? 1 : n % 1 == 0 && (n % 10 ==0 || (n % 10 >=5 && n % 10 <=9) || (n % 100 >=11 && n % 100 <=14 )) ? 2: 3);\n"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.reset_password_email
msgid "******-123-4567"
msgstr "******-123-4567"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.reset_password_email
msgid ""
",<br/><br/>\n"
"                                A password reset was requested for the Odoo account linked to this email.\n"
"                                You may change your password by following this link which will remain valid during"
msgstr ""
",<br/><br/>\n"
"                                Для облікового запису Odoo, пов’язаного з цією електронною адресою, було надіслано запит на скидання пароля.\n"
"Ви можете змінити свій пароль, перейшовши за цим посиланням, яке залишатиметься дійсним протягом"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.alert_login_new_device
msgid ""
",<br/><br/>\n"
"                        A new device was used to sign in to your account. <br/><br/>\n"
"                        Here are some details about the connection:<br/>"
msgstr ""
",<br/><br/>\n"
"                        Для входу в обліковий запис використано новий пристрій. <br/><br/>\n"
"                        Ось деякі подробиці про підключення:<br/>"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.reset_password_email
msgid "--<br/>Mitchell Admin"
msgstr "--<br/>Mitchell Admin"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.alert_login_new_device
msgid "111.222.333.444"
msgstr "111.222.333.444"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.reset_password_email
msgid "<span style=\"font-size: 10px;\">Your Account</span><br/>"
msgstr "<span style=\"font-size: 10px;\">Ваш обліковий запис</span><br/>"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.alert_login_new_device
msgid ""
"<span style=\"font-weight: bold;\">\n"
"                                    Browser:</span>"
msgstr ""
"<span style=\"font-weight: bold;\">\n"
"                                    Браузер:</span>"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.alert_login_new_device
msgid ""
"<span style=\"font-weight: bold;\">\n"
"                                    Location:</span>"
msgstr ""
"<span style=\"font-weight: bold;\">\n"
"                                    Розташування:</span>"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.alert_login_new_device
msgid ""
"<span style=\"font-weight: bold;\">\n"
"                                    Platform:</span>"
msgstr ""
"<span style=\"font-weight: bold;\">\n"
"                                    Платформа:</span>"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.alert_login_new_device
msgid ""
"<span style=\"font-weight: bold;\">\n"
"                                Date:</span>"
msgstr ""
"<span style=\"font-weight: bold;\">\n"
"                                Дата:</span>"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.alert_login_new_device
msgid ""
"<span style=\"font-weight: bold;\">\n"
"                                IP Address:</span>"
msgstr ""
"<span style=\"font-weight: bold;\">\n"
"                                IP адреса:</span>"

#. module: auth_signup
#: model:mail.template,body_html:auth_signup.mail_template_data_unregistered_users
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"background-color: #FFFFFF; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: #FFFFFF; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- CONTENT -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <t t-set=\"invited_users\" t-value=\"ctx.get('invited_users', [])\"/>\n"
"                <td style=\"text-align : left\">\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\">\n"
"                        Pending Invitations\n"
"                    </span><br/><br/>\n"
"                </td>\n"
"                <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                    <div>\n"
"                        Dear <t t-out=\"object.name or ''\">Mitchell Admin</t>,<br/> <br/>\n"
"                        You added the following user(s) to your database but they haven't registered yet:\n"
"                        <ul>\n"
"                            <t t-foreach=\"invited_users\" t-as=\"invited_user\">\n"
"                                <li t-out=\"invited_user or ''\"><EMAIL></li>\n"
"                            </t>\n"
"                        </ul>\n"
"                        Follow up with them so they can access your database and start working with you.\n"
"                        <br/><br/>\n"
"                        Have a nice day!<br/>\n"
"                        --<br/>The <t t-out=\"object.company_id.name or ''\">YourCompany</t> Team\n"
"                    </div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"</table>\n"
"            "
msgstr ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"background-color: #FFFFFF; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: #FFFFFF; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- CONTENT -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <t t-set=\"invited_users\" t-value=\"ctx.get('invited_users', [])\"/>\n"
"                <td style=\"text-align : left\">\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\">\n"
"                        Очікування запрошення\n"
"                    </span><br/><br/>\n"
"                </td>\n"
"                <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                    <div>\n"
"                        Шановний <t t-out=\"object.name or ''\">Mitchell Admin</t>,<br/> <br/>\n"
"                        Ви додали наступних користувачів до своєї бази даних, але вони ще не зареєструвалися:\n"
"                        <ul>\n"
"                            <t t-foreach=\"invited_users\" t-as=\"invited_user\">\n"
"                                <li t-out=\"invited_user or ''\"><EMAIL></li>\n"
"                            </t>\n"
"                        </ul>\n"
"                        Зверніться до них, щоб вони могли отримати доступ до вашої бази даних і почати з вами працювати.\n"
"                        <br/><br/>\n"
"                        Гарного дня!<br/>\n"
"                        --<br/>Команда <t t-out=\"object.company_id.name or ''\">YourCompany</t>\n"
"                    </div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"</table>\n"
"            "

#. module: auth_signup
#: model:mail.template,body_html:auth_signup.set_password_email
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #FFFFFF; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: #FFFFFF; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <span style=\"font-size: 10px;\">Welcome to Odoo</span><br/>\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\">\n"
"                        <t t-out=\"object.name or ''\">Marc Demo</t>\n"
"                    </span>\n"
"                </td><td valign=\"middle\" align=\"right\" t-if=\"not object.company_id.uses_default_logo\">\n"
"                    <img t-attf-src=\"/logo.png?company={{ object.company_id.id }}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"object.company_id.name\"/>\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- CONTENT -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                    <div>\n"
"                        Dear <t t-out=\"object.name or ''\">Marc Demo</t>,<br/><br/>\n"
"                        You have been invited by <t t-out=\"object.create_uid.name or ''\">OdooBot</t> of <t t-out=\"object.company_id.name or ''\">YourCompany</t> to connect on Odoo.\n"
"                        <div style=\"margin: 16px 0px 16px 0px;\">\n"
"                            <a t-att-href=\"object.partner_id._get_signup_url()\" t-attf-style=\"background-color: {{object.company_id.email_secondary_color or '#875A7B'}}; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                                Accept invitation\n"
"                            </a>\n"
"                        </div>\n"
"                        <b>  This link will remain valid during <t t-out=\"int(int(object.env['ir.config_parameter'].sudo().get_param('auth_signup.signup.validity.hours',144))/24)\"/> days </b> <br/>\n"
"                        <t t-set=\"website_url\" t-value=\"object.get_base_url()\"/>\n"
"                        Your Odoo domain is: <b><a t-att-href=\"website_url\" t-out=\"website_url or ''\">http://yourcompany.odoo.com</a></b><br/>\n"
"                        Your sign in email is: <b><a t-attf-href=\"/web/login?login={{ object.email }}\" target=\"_blank\" t-out=\"object.email or ''\"><EMAIL></a></b><br/><br/>\n"
"                        Never heard of Odoo? It’s an all-in-one business software loved by 7+ million users. It will considerably improve your experience at work and increase your productivity.\n"
"                        <br/><br/>\n"
"                        Have a look at the <a href=\"https://www.odoo.com/page/tour?utm_source=db&amp;utm_medium=auth\" style=\"color: #875A7B;\">Odoo Tour</a> to discover the tool.\n"
"                        <br/><br/>\n"
"                        Enjoy Odoo!<br/>\n"
"                        --<br/>The <t t-out=\"object.company_id.name or ''\">YourCompany</t> Team\n"
"                    </div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- FOOTER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\" align=\"left\">\n"
"                    <t t-out=\"object.company_id.name or ''\">YourCompany</t>\n"
"                </td></tr>\n"
"                <tr><td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"                    <t t-out=\"object.company_id.phone or ''\">******-123-4567</t>\n"
"                    <t t-if=\"object.company_id.email\">\n"
"                        | <a t-att-href=\"'mailto:%s' % object.company_id.email\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.company_id.email or ''\"><EMAIL></a>\n"
"                    </t>\n"
"                    <t t-if=\"object.company_id.website\">\n"
"                        | <a t-att-href=\"'%s' % object.company_id.website\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.company_id.website or ''\">http://www.example.com</a>\n"
"                    </t>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- POWERED BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"      <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"        Powered by <a target=\"_blank\" href=\"https://www.odoo.com?utm_source=db&amp;utm_medium=auth\" style=\"color: #875A7B;\">Odoo</a>\n"
"      </td></tr>\n"
"    </table>\n"
"</td></tr>\n"
"</table>"
msgstr ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #FFFFFF; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: #FFFFFF; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <span style=\"font-size: 10px;\">Вітаємо в Odoo</span><br/>\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\">\n"
"                        <t t-out=\"object.name or ''\">Marc Demo</t>\n"
"                    </span>\n"
"                </td><td valign=\"middle\" align=\"right\" t-if=\"not object.company_id.uses_default_logo\">\n"
"                    <img t-attf-src=\"/logo.png?company={{ object.company_id.id }}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"object.company_id.name\"/>\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- CONTENT -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                    <div>\n"
"                        Шановний <t t-out=\"object.name or ''\">Marc Demo</t>,<br/><br/>\n"
"                        Вас запросив <t t-out=\"object.create_uid.name or ''\">OdooBot</t> з <t t-out=\"object.company_id.name or ''\">YourCompany</t> приєднатися Odoo.\n"
"                        <div style=\"margin: 16px 0px 16px 0px;\">\n"
"                            <a t-att-href=\"object.partner_id._get_signup_url()\" t-attf-style=\"background-color: {{object.company_id.email_secondary_color or '#875A7B'}}; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                                Підтвердити запрошення\n"
"                            </a>\n"
"                        </div>\n"
"                        <b>  Це посилання залишатиметься дійсним протягом <t t-out=\"int(int(object.env['ir.config_parameter'].sudo().get_param('auth_signup.signup.validity.hours',144))/24)\"/> днів </b> <br/>\n"
"                        <t t-set=\"website_url\" t-value=\"object.get_base_url()\"/>\n"
"                        Ваш домен Odoo: <b><a t-att-href=\"website_url\" t-out=\"website_url or ''\">http://yourcompany.odoo.com</a></b><br/>\n"
"                        Ваш email: <b><a t-attf-href=\"/web/login?login={{ object.email }}\" target=\"_blank\" t-out=\"object.email or ''\"><EMAIL></a></b><br/><br/>\n"
"                        Ніколи не чули про Odoo? Це універсальне бізнес-програмне забезпечення, яке люблять понад 7 мільйонів користувачів. Воно значно покращить ваш досвід роботи та підвищить вашу продуктивність.\n"
"                        <br/><br/>\n"
"                        Перегляньте <a href=\"https://www.odoo.com/page/tour?utm_source=db&amp;utm_medium=auth\" style=\"color: #875A7B;\">Odoo Tour</a> щоб дізнатися про цей інструмент.\n"
"                        <br/><br/>\n"
"                        Насолоджуйтесь Odoo!<br/>\n"
"                        --<br/>Команда <t t-out=\"object.company_id.name or ''\">YourCompany</t>\n"
"                    </div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- FOOTER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\" align=\"left\">\n"
"                    <t t-out=\"object.company_id.name or ''\">YourCompany</t>\n"
"                </td></tr>\n"
"                <tr><td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"                    <t t-out=\"object.company_id.phone or ''\">******-123-4567</t>\n"
"                    <t t-if=\"object.company_id.email\">\n"
"                        | <a t-att-href=\"'mailto:%s' % object.company_id.email\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.company_id.email or ''\"><EMAIL></a>\n"
"                    </t>\n"
"                    <t t-if=\"object.company_id.website\">\n"
"                        | <a t-att-href=\"'%s' % object.company_id.website\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.company_id.website or ''\">http://www.example.com</a>\n"
"                    </t>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- POWERED BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"      <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"        Зроблено <a target=\"_blank\" href=\"https://www.odoo.com?utm_source=db&amp;utm_medium=auth\" style=\"color: #875A7B;\">Odoo</a>\n"
"      </td></tr>\n"
"    </table>\n"
"</td></tr>\n"
"</table>"

#. module: auth_signup
#: model:mail.template,body_html:auth_signup.mail_template_user_signup_account_created
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #FFFFFF; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: #FFFFFF; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <span style=\"font-size: 10px;\">Your Account</span><br/>\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\">\n"
"                        <t t-out=\"object.name or ''\">Marc Demo</t>\n"
"                    </span>\n"
"                </td><td valign=\"middle\" align=\"right\" t-if=\"not object.company_id.uses_default_logo\">\n"
"                    <img t-attf-src=\"/logo.png?company={{ object.company_id.id }}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"object.company_id.name\"/>\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- CONTENT -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                    <div>\n"
"                        Dear <t t-out=\"object.name or ''\">Marc Demo</t>,<br/><br/>\n"
"                        Your account has been successfully created!<br/>\n"
"                        Your login is <strong><t t-out=\"object.email or ''\"><EMAIL></t></strong><br/>\n"
"                        To gain access to your account, you can use the following link:\n"
"                        <div style=\"margin: 16px 0px 16px 0px;\">\n"
"                            <a t-attf-href=\"/web/login?auth_login={{object.email}}\" style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                                Go to My Account\n"
"                            </a>\n"
"                        </div>\n"
"                        Thanks,<br/>\n"
"                        <t t-if=\"user.signature\" data-o-mail-quote-container=\"1\">\n"
"                            <br/>\n"
"                            <t t-out=\"user.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"                        </t>\n"
"                    </div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- FOOTER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\" align=\"left\">\n"
"                    <t t-out=\"object.company_id.name or ''\">YourCompany</t>\n"
"                </td></tr>\n"
"                <tr><td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"                    <t t-out=\"object.company_id.phone or ''\">******-123-4567</t>\n"
"                    <t t-if=\"object.company_id.email\">\n"
"                        | <a t-attf-href=\"'mailto:%s' % {{ object.company_id.email }}\" style=\"text-decoration:none; color: #454748;\"><t t-out=\"object.company_id.email or ''\"><EMAIL></t></a>\n"
"                    </t>\n"
"                    <t t-if=\"object.company_id.website\">\n"
"                        | <a t-attf-href=\"'%s' % {{ object.company_id.website }}\" style=\"text-decoration:none; color: #454748;\">\n"
"                            <t t-out=\"object.company_id.website or ''\">http://www.example.com</t>\n"
"                        </a>\n"
"                    </t>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- POWERED BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"      <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"        Powered by <a target=\"_blank\" href=\"https://www.odoo.com?utm_source=db&amp;utm_medium=auth\" style=\"color: #875A7B;\">Odoo</a>\n"
"      </td></tr>\n"
"    </table>\n"
"</td></tr>\n"
"</table>"
msgstr ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #FFFFFF; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: #FFFFFF; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <span style=\"font-size: 10px;\">Ваш обліковий запис</span><br/>\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\">\n"
"                        <t t-out=\"object.name or ''\">Marc Demo</t>\n"
"                    </span>\n"
"                </td><td valign=\"middle\" align=\"right\" t-if=\"not object.company_id.uses_default_logo\">\n"
"                    <img t-attf-src=\"/logo.png?company={{ object.company_id.id }}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"object.company_id.name\"/>\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- CONTENT -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                    <div>\n"
"                        Шановний <t t-out=\"object.name or ''\">Marc Demo</t>,<br/><br/>\n"
"                        Ваш обліковий запис успішно створено!<br/>\n"
"                        Ваш логін <strong><t t-out=\"object.email or ''\"><EMAIL></t></strong><br/>\n"
"                        Щоб отримати доступ до свого облікового запису, ви можете скористатися наступним посиланням:\n"
"                        <div style=\"margin: 16px 0px 16px 0px;\">\n"
"                            <a t-attf-href=\"/web/login?auth_login={{object.email}}\" style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                                Перейти до мого облікового запису\n"
"                            </a>\n"
"                        </div>\n"
"                        Дякуємо,<br/>\n"
"                        <t t-if=\"user.signature\" data-o-mail-quote-container=\"1\">\n"
"                            <br/>\n"
"                            <t t-out=\"user.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"                        </t>\n"
"                    </div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- FOOTER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\" align=\"left\">\n"
"                    <t t-out=\"object.company_id.name or ''\">YourCompany</t>\n"
"                </td></tr>\n"
"                <tr><td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"                    <t t-out=\"object.company_id.phone or ''\">******-123-4567</t>\n"
"                    <t t-if=\"object.company_id.email\">\n"
"                        | <a t-attf-href=\"'mailto:%s' % {{ object.company_id.email }}\" style=\"text-decoration:none; color: #454748;\"><t t-out=\"object.company_id.email or ''\"><EMAIL></t></a>\n"
"                    </t>\n"
"                    <t t-if=\"object.company_id.website\">\n"
"                        | <a t-attf-href=\"'%s' % {{ object.company_id.website }}\" style=\"text-decoration:none; color: #454748;\">\n"
"                            <t t-out=\"object.company_id.website or ''\">http://www.example.com</t>\n"
"                        </a>\n"
"                    </t>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- POWERED BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"      <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"        Зроблено <a target=\"_blank\" href=\"https://www.odoo.com?utm_source=db&amp;utm_medium=auth\" style=\"color: #875A7B;\">Odoo</a>\n"
"      </td></tr>\n"
"    </table>\n"
"</td></tr>\n"
"</table>"

#. module: auth_signup
#. odoo-python
#: code:addons/auth_signup/models/res_users.py:0
msgid "A reset password link was send by email"
msgstr "Посилання для скидання пароля було надіслано електронною поштою"

#. module: auth_signup
#. odoo-python
#: code:addons/auth_signup/models/res_users.py:0
msgid "A signup link was send by email"
msgstr "Посилання для реєстрації було надіслано електронною поштою"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.signup
msgid "Already have an account?"
msgstr "Вже є аккаунт?"

#. module: auth_signup
#. odoo-python
#: code:addons/auth_signup/controllers/main.py:0
msgid "Another user is already registered using this email address."
msgstr "Іншого користувача з такою електронною поштою вже зареєстровано."

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.reset_password
msgid "Back to Login"
msgstr "Назад до входу"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.alert_login_new_device
msgid "Browser"
msgstr "Браузер"

#. module: auth_signup
#. odoo-python
#: code:addons/auth_signup/models/res_users.py:0
msgid "Cannot send email: user %s has no email address."
msgstr ""
"Неможливо відправити лист: користувач %s не має адреси електронної пошти."

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.reset_password_email
msgid "Change password"
msgstr "Пароль"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.alert_login_new_device
msgid "City, Region, Country"
msgstr "Місто, регіон, країна"

#. module: auth_signup
#: model:ir.model,name:auth_signup.model_res_config_settings
msgid "Config Settings"
msgstr "Налаштування"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.fields
msgid "Confirm Password"
msgstr "Підтвердити пароль"

#. module: auth_signup
#: model:ir.model.fields.selection,name:auth_signup.selection__res_users__state__active
msgid "Confirmed"
msgstr "Підтверджено"

#. module: auth_signup
#: model:ir.model,name:auth_signup.model_res_partner
msgid "Contact"
msgstr "Контакт"

#. module: auth_signup
#. odoo-python
#: code:addons/auth_signup/models/res_users.py:0
msgid ""
"Could not contact the mail server, please check your outgoing email server "
"configuration"
msgstr ""
"Не вдалося зв’язатися з поштовим сервером, перевірте конфігурацію сервера "
"вихідної пошти"

#. module: auth_signup
#. odoo-python
#: code:addons/auth_signup/controllers/main.py:0
msgid "Could not create a new account."
msgstr "Не можливо створити новий обліковий запис."

#. module: auth_signup
#. odoo-python
#: code:addons/auth_signup/controllers/main.py:0
msgid "Could not reset your password"
msgstr "Не можливо cкинути пароль"

#. module: auth_signup
#: model:ir.model.fields,field_description:auth_signup.field_res_config_settings__auth_signup_uninvited
msgid "Customer Account"
msgstr "Клієнтський обліковий запис"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.alert_login_new_device
#: model_terms:ir.ui.view,arch_db:auth_signup.reset_password_email
msgid "Dear"
msgstr "Шановний"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.res_config_settings_view_form
msgid "Default Access Rights"
msgstr "Права доступу за замовчуванням"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.login
msgid "Don't have an account?"
msgstr "Не маєте облікового запису?"

#. module: auth_signup
#: model:ir.model.fields,field_description:auth_signup.field_res_config_settings__auth_signup_reset_password
#: model_terms:ir.ui.view,arch_db:auth_signup.res_config_settings_view_form
msgid "Enable password reset from Login page"
msgstr "Дозволити скидання пароля зі сторінки входу"

#. module: auth_signup
#: model:ir.model.fields.selection,name:auth_signup.selection__res_config_settings__auth_signup_uninvited__b2c
msgid "Free sign up"
msgstr "Безкоштовна рестрація"

#. module: auth_signup
#: model:ir.model,name:auth_signup.model_ir_http
msgid "HTTP Routing"
msgstr "Маршрутизація HTTP"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.reset_password_email
msgid ""
"If you do not expect this, you can safely ignore this email.<br/><br/>\n"
"                                Thanks,"
msgstr ""
"Якщо ви цього не очікуєте, можете сміливо ігнорувати цей електронний лист.<br/><br/>\n"
"                                Дякуємо,"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.alert_login_new_device
msgid ""
"If you don't recognize it, you should change your password immediately via "
"this link:<br/>"
msgstr ""
"Якщо ви його не впізнаєте, негайно змініть свій пароль за цим "
"посиланням:<br/>"

#. module: auth_signup
#. odoo-python
#: code:addons/auth_signup/models/res_users.py:0
msgid "In %(country)s"
msgstr "В %(country)s"

#. module: auth_signup
#. odoo-python
#: code:addons/auth_signup/controllers/main.py:0
msgid "Invalid signup token"
msgstr "Невірний вхід токену"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.res_config_settings_view_form
msgid "Let your customers log in to see their documents"
msgstr "Дозвольте вашим клієнтам увійти, щоби переглянути їх документи"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.alert_login_new_device
#: model_terms:ir.ui.view,arch_db:auth_signup.reset_password_email
msgid "Marc Demo"
msgstr "Marc Demo"

#. module: auth_signup
#. odoo-python
#: code:addons/auth_signup/models/res_users.py:0
msgid "Multiple accounts found for this login"
msgstr "Для цього логіна знайдено кілька облікових записів."

#. module: auth_signup
#. odoo-python
#: code:addons/auth_signup/models/res_users.py:0
msgid "Near %(city)s, %(region)s, %(country)s"
msgstr "Поруч %(city)s, %(region)s, %(country)s"

#. module: auth_signup
#. odoo-python
#: code:addons/auth_signup/models/res_users.py:0
msgid "Near %(region)s, %(country)s"
msgstr "Поруч %(region)s, %(country)s"

#. module: auth_signup
#: model:ir.model.fields.selection,name:auth_signup.selection__res_users__state__new
msgid "Never Connected"
msgstr "Ніколи не входив"

#. module: auth_signup
#. odoo-python
#: code:addons/auth_signup/models/res_users.py:0
msgid "New Connection to your Account"
msgstr "Нове підключення до вашого облікового запису"

#. module: auth_signup
#. odoo-python
#: code:addons/auth_signup/models/res_users.py:0
msgid "No account found for this login"
msgstr "Жодного облікового запису з таким логіном не знайдено"

#. module: auth_signup
#. odoo-python
#: code:addons/auth_signup/controllers/main.py:0
msgid "No login provided."
msgstr "Логін не введено."

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.alert_login_new_device
msgid "OS"
msgstr "OS"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.alert_login_new_device
#: model_terms:ir.ui.view,arch_db:auth_signup.reset_password_email
msgid "Odoo"
msgstr "Odoo"

#. module: auth_signup
#: model:ir.model.fields.selection,name:auth_signup.selection__res_config_settings__auth_signup_uninvited__b2b
msgid "On invitation"
msgstr "На запрошення"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.alert_login_new_device
msgid "Otherwise, you can safely ignore this email."
msgstr "В іншому випадку ви можете спокійно ігнорувати цей електронний лист."

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.fields
msgid "Password"
msgstr "Пароль"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.res_config_settings_view_form
msgid "Password Reset"
msgstr "Скидання паролю"

#. module: auth_signup
#. odoo-python
#: code:addons/auth_signup/models/res_users.py:0
msgid "Password reset"
msgstr "Скидання паролю"

#. module: auth_signup
#. odoo-python
#: code:addons/auth_signup/controllers/main.py:0
msgid "Password reset instructions sent to your email"
msgstr "Інструкції щодо зміни пароля надіслано на вашу електронну пошту"

#. module: auth_signup
#. odoo-python
#: code:addons/auth_signup/controllers/main.py:0
msgid "Passwords do not match; please retype them."
msgstr "Паролі не збігаються; будь ласка, введіть їх знову."

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.alert_login_new_device
#: model_terms:ir.ui.view,arch_db:auth_signup.reset_password_email
msgid "Powered by"
msgstr "Зроблено"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.login_successful
msgid "Registration successful."
msgstr "Реєстрація успішна."

#. module: auth_signup
#: model:mail.template,subject:auth_signup.mail_template_data_unregistered_users
msgid "Reminder for unregistered users"
msgstr "Нагадування для незареєстрованих користувачів"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.alert_login_new_device
#: model_terms:ir.ui.view,arch_db:auth_signup.login
#: model_terms:ir.ui.view,arch_db:auth_signup.reset_password
msgid "Reset Password"
msgstr "Скинути пароль"

#. module: auth_signup
#: model:ir.actions.server,name:auth_signup.action_send_password_reset_instructions
#: model_terms:ir.ui.view,arch_db:auth_signup.res_users_view_form
msgid "Send Password Reset Instructions"
msgstr "Надіслати інструкцію зі скидання паролю"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.res_users_view_form
msgid "Send an Invitation Email"
msgstr "Відправили лист із запрошенням"

#. module: auth_signup
#: model:mail.template,description:auth_signup.mail_template_data_unregistered_users
msgid ""
"Sent automatically to admin if new user haven't responded to the invitation"
msgstr ""
"Автоматично надсилається адміністратору, якщо новий користувач не відповів "
"на запрошення"

#. module: auth_signup
#: model:mail.template,description:auth_signup.set_password_email
msgid "Sent to new user after you invited them"
msgstr "Надіслано новому користувачеві після того, як ви його запросили"

#. module: auth_signup
#: model:mail.template,description:auth_signup.mail_template_user_signup_account_created
msgid "Sent to portal user who registered themselves"
msgstr "Надіслано користувачеві порталу, який зареєструвався самостійно"

#. module: auth_signup
#: model:mail.template,name:auth_signup.mail_template_user_signup_account_created
msgid "Settings: New Portal Sign Up"
msgstr "Налаштування: Новий вхід на портал"

#. module: auth_signup
#: model:mail.template,name:auth_signup.set_password_email
msgid "Settings: New User Invite"
msgstr "Налаштування: Запрошення нового користувача"

#. module: auth_signup
#: model:mail.template,name:auth_signup.mail_template_data_unregistered_users
msgid "Settings: Unregistered User Reminder"
msgstr "Налаштування: нагадування незареєстрованого користувача"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.signup
msgid "Sign up"
msgstr "Реєстрація"

#. module: auth_signup
#: model:ir.model.fields,field_description:auth_signup.field_res_partner__signup_type
#: model:ir.model.fields,field_description:auth_signup.field_res_users__signup_type
msgid "Signup Token Type"
msgstr "Тип талону реєстрації"

#. module: auth_signup
#. odoo-python
#: code:addons/auth_signup/models/res_users.py:0
msgid "Signup is not allowed for uninvited users"
msgstr "Реєстрація незапрошених користувачів заборонена"

#. module: auth_signup
#. odoo-python
#: code:addons/auth_signup/models/res_partner.py:0
msgid "Signup token '%s' is not valid or expired"
msgstr "Токен входу '%s' недійсний або протермінований"

#. module: auth_signup
#. odoo-python
#: code:addons/auth_signup/models/res_users.py:0
msgid "Signup: invalid template user"
msgstr "Реєстрація: недійсний користувач шаблону"

#. module: auth_signup
#. odoo-python
#: code:addons/auth_signup/models/res_users.py:0
msgid "Signup: no login given for new user"
msgstr "Реєстрація: для нового користувача не введено логін"

#. module: auth_signup
#. odoo-python
#: code:addons/auth_signup/models/res_users.py:0
msgid "Signup: no name or partner given for new user"
msgstr "Реєстрація: не вказано ім'я або партнера для нового користувача"

#. module: auth_signup
#: model:ir.model.fields,field_description:auth_signup.field_res_users__state
msgid "Status"
msgstr "Статус"

#. module: auth_signup
#. odoo-python
#: code:addons/auth_signup/controllers/main.py:0
msgid "Suspicious activity detected by Google reCaptcha."
msgstr "Google reCaptcha виявила підозрілу активність."

#. module: auth_signup
#: model:ir.model.fields,field_description:auth_signup.field_res_config_settings__auth_signup_template_user_id
msgid "Template user for new users created through signup"
msgstr "Шаблон користувача для створення користувачів під час реєстрації"

#. module: auth_signup
#. odoo-python
#: code:addons/auth_signup/controllers/main.py:0
msgid "The form was not properly filled in."
msgstr "Форма не була належним чином заповнена."

#. module: auth_signup
#. odoo-python
#: code:addons/auth_signup/models/res_users.py:0
msgid ""
"There was an error when trying to deliver your Email, please check your "
"configuration"
msgstr ""
"Під час спроби доставити вашу електронну пошту сталася помилка, перевірте "
"конфігурацію"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.res_config_settings_view_form
msgid ""
"To send invitations in B2B mode, open a contact or select several ones in "
"list view and click on 'Portal Access Management' option in the dropdown "
"menu *Action*."
msgstr ""
"Щоб надіслати запрошення в режимі B2B, відкрийте контакт або виберіть кілька"
" у списку та натисніть на 'Управління доступом до порталу' у випадаючому "
"меню *Дія*."

#. module: auth_signup
#: model:ir.model,name:auth_signup.model_res_users
msgid "User"
msgstr "Користувач"

#. module: auth_signup
#: model:ir.actions.server,name:auth_signup.ir_cron_auth_signup_send_pending_user_reminder_ir_actions_server
msgid "Users: Notify About Unregistered Users"
msgstr "Користувачі: Повідомляти про незареєстрованих користувачів"

#. module: auth_signup
#: model:mail.template,subject:auth_signup.mail_template_user_signup_account_created
msgid "Welcome to {{ object.company_id.name }}!"
msgstr "Вітаємо в {{ object.company_id.name }}!"

#. module: auth_signup
#. odoo-python
#: code:addons/auth_signup/models/res_users.py:0
msgid "You cannot perform this action on an archived user."
msgstr "Ви не можете виконати цю дію на архівованому користувачі."

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.fields
#: model_terms:ir.ui.view,arch_db:auth_signup.reset_password
msgid "Your Email"
msgstr "Ваша електронна пошта"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.fields
msgid "Your Name"
msgstr "Ваше ім’я"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.reset_password_email
msgid "YourCompany"
msgstr "YourCompany"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.alert_login_new_device
msgid "day, month dd, yyyy - hh:mm:ss (GMT)"
msgstr "день, місяць dd, yyyy - hh:mm:ss (GMT)"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.fields
msgid "e.g. John Doe"
msgstr "наприклад, Степан Бандера"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.reset_password_email
msgid "hours:<br/>"
msgstr "годин:<br/>"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.reset_password_email
msgid "http://www.example.com"
msgstr "http://www.example.com"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.reset_password_email
msgid "<EMAIL>"
msgstr "<EMAIL>"

#. module: auth_signup
#: model:mail.template,subject:auth_signup.set_password_email
msgid ""
"{{ object.create_uid.name }} from {{ object.company_id.name }} invites you "
"to connect to Odoo"
msgstr ""
"{{ object.create_uid.name }} від {{ object.company_id.name }} запросив вас "
"приєднатися до Odoo"
