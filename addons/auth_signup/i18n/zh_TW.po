# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* auth_signup
# 
# Translators:
# <PERSON><PERSON>, 2025
# <PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-03-19 20:36+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON>, 2025\n"
"Language-Team: Chinese (Taiwan) (https://app.transifex.com/odoo/teams/41243/zh_TW/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_TW\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.reset_password_email
msgid "******-123-4567"
msgstr "(+852) 2632 9300"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.reset_password_email
msgid ""
",<br/><br/>\n"
"                                A password reset was requested for the Odoo account linked to this email.\n"
"                                You may change your password by following this link which will remain valid during"
msgstr ""
"：<br/><br/>\n"
"                                剛收到要求，為使用此電郵地址的 Odoo 帳戶重設密碼。\n"
"                                你可透過以下連結更改密碼。連結有效期為 "

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.alert_login_new_device
msgid ""
",<br/><br/>\n"
"                        A new device was used to sign in to your account. <br/><br/>\n"
"                        Here are some details about the connection:<br/>"
msgstr ""
"：<br/><br/>\n"
"                        有人使用新的裝置登入你的帳戶。<br/><br/>\n"
"                        該次連線的部份資料如下：<br/>"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.reset_password_email
msgid "--<br/>Mitchell Admin"
msgstr "--<br/>Mitchell Admin"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.alert_login_new_device
msgid "111.222.333.444"
msgstr "111.222.333.444"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.reset_password_email
msgid "<span style=\"font-size: 10px;\">Your Account</span><br/>"
msgstr "<span style=\"font-size: 10px;\">你的帳戶</span><br/>"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.alert_login_new_device
msgid ""
"<span style=\"font-weight: bold;\">\n"
"                                    Browser:</span>"
msgstr ""
"<span style=\"font-weight: bold;\">\n"
"                                    瀏覽器：</span>"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.alert_login_new_device
msgid ""
"<span style=\"font-weight: bold;\">\n"
"                                    Location:</span>"
msgstr ""
"<span style=\"font-weight: bold;\">\n"
"                                    地點 / 位置：</span>"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.alert_login_new_device
msgid ""
"<span style=\"font-weight: bold;\">\n"
"                                    Platform:</span>"
msgstr ""
"<span style=\"font-weight: bold;\">\n"
"                                    平台：</span>"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.alert_login_new_device
msgid ""
"<span style=\"font-weight: bold;\">\n"
"                                Date:</span>"
msgstr ""
"<span style=\"font-weight: bold;\">\n"
"                                日期：</span>"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.alert_login_new_device
msgid ""
"<span style=\"font-weight: bold;\">\n"
"                                IP Address:</span>"
msgstr ""
"<span style=\"font-weight: bold;\">\n"
"                                IP 位址：</span>"

#. module: auth_signup
#: model:mail.template,body_html:auth_signup.mail_template_data_unregistered_users
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"background-color: #FFFFFF; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: #FFFFFF; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- CONTENT -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <t t-set=\"invited_users\" t-value=\"ctx.get('invited_users', [])\"/>\n"
"                <td style=\"text-align : left\">\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\">\n"
"                        Pending Invitations\n"
"                    </span><br/><br/>\n"
"                </td>\n"
"                <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                    <div>\n"
"                        Dear <t t-out=\"object.name or ''\">Mitchell Admin</t>,<br/> <br/>\n"
"                        You added the following user(s) to your database but they haven't registered yet:\n"
"                        <ul>\n"
"                            <t t-foreach=\"invited_users\" t-as=\"invited_user\">\n"
"                                <li t-out=\"invited_user or ''\"><EMAIL></li>\n"
"                            </t>\n"
"                        </ul>\n"
"                        Follow up with them so they can access your database and start working with you.\n"
"                        <br/><br/>\n"
"                        Have a nice day!<br/>\n"
"                        --<br/>The <t t-out=\"object.company_id.name or ''\">YourCompany</t> Team\n"
"                    </div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"</table>\n"
"            "
msgstr ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"background-color: #FFFFFF; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: #FFFFFF; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- CONTENT -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <t t-set=\"invited_users\" t-value=\"ctx.get('invited_users', [])\"/>\n"
"                <td style=\"text-align : left\">\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\">\n"
"                        尚待註冊邀請\n"
"                    </span><br/><br/>\n"
"                </td>\n"
"                <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                    <div>\n"
"                        <t t-out=\"object.name or ''\">Mitchell Admin</t> 你好！<br/> <br/>\n"
"                        你已將下列用戶加至資料庫，但他們尚未註冊：\n"
"                        <ul>\n"
"                            <t t-foreach=\"invited_users\" t-as=\"invited_user\">\n"
"                                <li t-out=\"invited_user or ''\"><EMAIL></li>\n"
"                            </t>\n"
"                        </ul>\n"
"                        你可向他們跟進，以便他們能存取你的資料庫並開始與你合作。\n"
"                        <br/><br/>\n"
"                        祝工作順利！<br/>\n"
"                        --<br/><t t-out=\"object.company_id.name or ''\">YourCompany</t> 團隊\n"
"                    </div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"</table>\n"
"            "

#. module: auth_signup
#: model:mail.template,body_html:auth_signup.set_password_email
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #FFFFFF; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: #FFFFFF; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <span style=\"font-size: 10px;\">Welcome to Odoo</span><br/>\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\">\n"
"                        <t t-out=\"object.name or ''\">Marc Demo</t>\n"
"                    </span>\n"
"                </td><td valign=\"middle\" align=\"right\" t-if=\"not object.company_id.uses_default_logo\">\n"
"                    <img t-attf-src=\"/logo.png?company={{ object.company_id.id }}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"object.company_id.name\"/>\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- CONTENT -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                    <div>\n"
"                        Dear <t t-out=\"object.name or ''\">Marc Demo</t>,<br/><br/>\n"
"                        You have been invited by <t t-out=\"object.create_uid.name or ''\">OdooBot</t> of <t t-out=\"object.company_id.name or ''\">YourCompany</t> to connect on Odoo.\n"
"                        <div style=\"margin: 16px 0px 16px 0px;\">\n"
"                            <a t-att-href=\"object.partner_id._get_signup_url()\" t-attf-style=\"background-color: {{object.company_id.email_secondary_color or '#875A7B'}}; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                                Accept invitation\n"
"                            </a>\n"
"                        </div>\n"
"                        <b>  This link will remain valid during <t t-out=\"int(int(object.env['ir.config_parameter'].sudo().get_param('auth_signup.signup.validity.hours',144))/24)\"/> days </b> <br/>\n"
"                        <t t-set=\"website_url\" t-value=\"object.get_base_url()\"/>\n"
"                        Your Odoo domain is: <b><a t-att-href=\"website_url\" t-out=\"website_url or ''\">http://yourcompany.odoo.com</a></b><br/>\n"
"                        Your sign in email is: <b><a t-attf-href=\"/web/login?login={{ object.email }}\" target=\"_blank\" t-out=\"object.email or ''\"><EMAIL></a></b><br/><br/>\n"
"                        Never heard of Odoo? It’s an all-in-one business software loved by 7+ million users. It will considerably improve your experience at work and increase your productivity.\n"
"                        <br/><br/>\n"
"                        Have a look at the <a href=\"https://www.odoo.com/page/tour?utm_source=db&amp;utm_medium=auth\" style=\"color: #875A7B;\">Odoo Tour</a> to discover the tool.\n"
"                        <br/><br/>\n"
"                        Enjoy Odoo!<br/>\n"
"                        --<br/>The <t t-out=\"object.company_id.name or ''\">YourCompany</t> Team\n"
"                    </div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- FOOTER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\" align=\"left\">\n"
"                    <t t-out=\"object.company_id.name or ''\">YourCompany</t>\n"
"                </td></tr>\n"
"                <tr><td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"                    <t t-out=\"object.company_id.phone or ''\">******-123-4567</t>\n"
"                    <t t-if=\"object.company_id.email\">\n"
"                        | <a t-att-href=\"'mailto:%s' % object.company_id.email\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.company_id.email or ''\"><EMAIL></a>\n"
"                    </t>\n"
"                    <t t-if=\"object.company_id.website\">\n"
"                        | <a t-att-href=\"'%s' % object.company_id.website\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.company_id.website or ''\">http://www.example.com</a>\n"
"                    </t>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- POWERED BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"      <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"        Powered by <a target=\"_blank\" href=\"https://www.odoo.com?utm_source=db&amp;utm_medium=auth\" style=\"color: #875A7B;\">Odoo</a>\n"
"      </td></tr>\n"
"    </table>\n"
"</td></tr>\n"
"</table>"
msgstr ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #FFFFFF; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: #FFFFFF; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <span style=\"font-size: 10px;\">歡迎使用 Odoo</span><br/>\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\">\n"
"                        <t t-out=\"object.name or ''\">Marc Demo</t>\n"
"                    </span>\n"
"                </td><td valign=\"middle\" align=\"right\" t-if=\"not object.company_id.uses_default_logo\">\n"
"                    <img t-attf-src=\"/logo.png?company={{ object.company_id.id }}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"object.company_id.name\"/>\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- CONTENT -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                    <div>\n"
"                        <t t-out=\"object.name or ''\">Marc Demo</t> 你好！<br/><br/>\n"
"                        你已獲 <t t-out=\"object.company_id.name or ''\">YourCompany</t> 的用戶 <t t-out=\"object.create_uid.name or ''\">OdooBot</t> 邀請，連接使用 Odoo。\n"
"                        <div style=\"margin: 16px 0px 16px 0px;\">\n"
"                            <a t-att-href=\"object.partner_id._get_signup_url()\" t-attf-style=\"background-color: {{object.company_id.email_secondary_color or '#875A7B'}}; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                                接納邀請\n"
"                            </a>\n"
"                        </div>\n"
"                        <b>  請注意：連結有效期為 <t t-out=\"int(int(object.env['ir.config_parameter'].sudo().get_param('auth_signup.signup.validity.hours',144))/24)\"/> 天 </b> <br/>\n"
"                        <t t-set=\"website_url\" t-value=\"object.get_base_url()\"/>\n"
"                        你的 Odoo 網域： <b><a t-att-href=\"website_url\" t-out=\"website_url or ''\">http://yourcompany.odoo.com</a></b><br/>\n"
"                        你的登入電郵地址： <b><a t-attf-href=\"/web/login?login={{ object.email }}\" target=\"_blank\" t-out=\"object.email or ''\"><EMAIL></a></b><br/><br/>\n"
"                        還未聽說過 Odoo？這款結合所有功能的商業管理軟件，受全球超過 700 萬用戶喜愛。它能夠顯著改善你的工作體驗，助你提升生產力。\n"
"                        <br/><br/>\n"
"                        不妨看看 <a href=\"https://www.odoo.com/page/tour?utm_source=db&amp;utm_medium=auth\" style=\"color: #875A7B;\">Odoo 歡迎導覽</a>，發掘這款工具的無限潛能。\n"
"                        <br/><br/>\n"
"                        希望你喜歡使用 Odoo！<br/>\n"
"                        --<br/><t t-out=\"object.company_id.name or ''\">YourCompany</t> 團隊\n"
"                    </div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- FOOTER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\" align=\"left\">\n"
"                    <t t-out=\"object.company_id.name or ''\">YourCompany</t>\n"
"                </td></tr>\n"
"                <tr><td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"                    <t t-out=\"object.company_id.phone or ''\">******-123-4567</t>\n"
"                    <t t-if=\"object.company_id.email\">\n"
"                        | <a t-att-href=\"'mailto:%s' % object.company_id.email\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.company_id.email or ''\"><EMAIL></a>\n"
"                    </t>\n"
"                    <t t-if=\"object.company_id.website\">\n"
"                        | <a t-att-href=\"'%s' % object.company_id.website\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.company_id.website or ''\">http://www.example.com</a>\n"
"                    </t>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- POWERED BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"      <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"        由 <a target=\"_blank\" href=\"https://www.odoo.com?utm_source=db&amp;utm_medium=auth\" style=\"color: #875A7B;\">Odoo</a> 驅動\n"
"      </td></tr>\n"
"    </table>\n"
"</td></tr>\n"
"</table>"

#. module: auth_signup
#: model:mail.template,body_html:auth_signup.mail_template_user_signup_account_created
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #FFFFFF; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: #FFFFFF; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <span style=\"font-size: 10px;\">Your Account</span><br/>\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\">\n"
"                        <t t-out=\"object.name or ''\">Marc Demo</t>\n"
"                    </span>\n"
"                </td><td valign=\"middle\" align=\"right\" t-if=\"not object.company_id.uses_default_logo\">\n"
"                    <img t-attf-src=\"/logo.png?company={{ object.company_id.id }}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"object.company_id.name\"/>\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- CONTENT -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                    <div>\n"
"                        Dear <t t-out=\"object.name or ''\">Marc Demo</t>,<br/><br/>\n"
"                        Your account has been successfully created!<br/>\n"
"                        Your login is <strong><t t-out=\"object.email or ''\"><EMAIL></t></strong><br/>\n"
"                        To gain access to your account, you can use the following link:\n"
"                        <div style=\"margin: 16px 0px 16px 0px;\">\n"
"                            <a t-attf-href=\"/web/login?auth_login={{object.email}}\" style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                                Go to My Account\n"
"                            </a>\n"
"                        </div>\n"
"                        Thanks,<br/>\n"
"                        <t t-if=\"user.signature\" data-o-mail-quote-container=\"1\">\n"
"                            <br/>\n"
"                            <t t-out=\"user.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"                        </t>\n"
"                    </div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- FOOTER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\" align=\"left\">\n"
"                    <t t-out=\"object.company_id.name or ''\">YourCompany</t>\n"
"                </td></tr>\n"
"                <tr><td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"                    <t t-out=\"object.company_id.phone or ''\">******-123-4567</t>\n"
"                    <t t-if=\"object.company_id.email\">\n"
"                        | <a t-attf-href=\"'mailto:%s' % {{ object.company_id.email }}\" style=\"text-decoration:none; color: #454748;\"><t t-out=\"object.company_id.email or ''\"><EMAIL></t></a>\n"
"                    </t>\n"
"                    <t t-if=\"object.company_id.website\">\n"
"                        | <a t-attf-href=\"'%s' % {{ object.company_id.website }}\" style=\"text-decoration:none; color: #454748;\">\n"
"                            <t t-out=\"object.company_id.website or ''\">http://www.example.com</t>\n"
"                        </a>\n"
"                    </t>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- POWERED BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"      <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"        Powered by <a target=\"_blank\" href=\"https://www.odoo.com?utm_source=db&amp;utm_medium=auth\" style=\"color: #875A7B;\">Odoo</a>\n"
"      </td></tr>\n"
"    </table>\n"
"</td></tr>\n"
"</table>"
msgstr ""

#. module: auth_signup
#. odoo-python
#: code:addons/auth_signup/models/res_users.py:0
msgid "A reset password link was send by email"
msgstr "重設密碼連結已透過電子郵件傳送"

#. module: auth_signup
#. odoo-python
#: code:addons/auth_signup/models/res_users.py:0
msgid "A signup link was send by email"
msgstr "註冊連結已透過電子郵件傳送"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.signup
msgid "Already have an account?"
msgstr "已經具有帳戶？"

#. module: auth_signup
#. odoo-python
#: code:addons/auth_signup/controllers/main.py:0
msgid "Another user is already registered using this email address."
msgstr "已有另一名用戶使用此電郵地址註冊。"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.reset_password
msgid "Back to Login"
msgstr "返回登入頁面"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.alert_login_new_device
msgid "Browser"
msgstr "瀏覽器"

#. module: auth_signup
#. odoo-python
#: code:addons/auth_signup/models/res_users.py:0
msgid "Cannot send email: user %s has no email address."
msgstr "未能傳送電子郵件：使用者 %s 沒有電郵地址。"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.reset_password_email
msgid "Change password"
msgstr "更改密碼"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.alert_login_new_device
msgid "City, Region, Country"
msgstr "城市、區域、國家"

#. module: auth_signup
#: model:ir.model,name:auth_signup.model_res_config_settings
msgid "Config Settings"
msgstr "配置設定"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.fields
msgid "Confirm Password"
msgstr "確認密碼"

#. module: auth_signup
#: model:ir.model.fields.selection,name:auth_signup.selection__res_users__state__active
msgid "Confirmed"
msgstr "已確認"

#. module: auth_signup
#: model:ir.model,name:auth_signup.model_res_partner
msgid "Contact"
msgstr "聯絡人"

#. module: auth_signup
#. odoo-python
#: code:addons/auth_signup/models/res_users.py:0
msgid ""
"Could not contact the mail server, please check your outgoing email server "
"configuration"
msgstr "無法聯絡郵件伺服器，請檢查您的外發電子郵件伺服器配置"

#. module: auth_signup
#. odoo-python
#: code:addons/auth_signup/controllers/main.py:0
msgid "Could not create a new account."
msgstr "不能建立一個新帳號."

#. module: auth_signup
#. odoo-python
#: code:addons/auth_signup/controllers/main.py:0
msgid "Could not reset your password"
msgstr "無法重設密碼"

#. module: auth_signup
#: model:ir.model.fields,field_description:auth_signup.field_res_config_settings__auth_signup_uninvited
msgid "Customer Account"
msgstr "客戶帳戶"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.alert_login_new_device
#: model_terms:ir.ui.view,arch_db:auth_signup.reset_password_email
msgid "Dear"
msgstr "親愛的"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.res_config_settings_view_form
msgid "Default Access Rights"
msgstr "預設存取權限"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.login
msgid "Don't have an account?"
msgstr "還沒有帳戶？"

#. module: auth_signup
#: model:ir.model.fields,field_description:auth_signup.field_res_config_settings__auth_signup_reset_password
#: model_terms:ir.ui.view,arch_db:auth_signup.res_config_settings_view_form
msgid "Enable password reset from Login page"
msgstr "允許在登入頁使用密碼重新設定功能"

#. module: auth_signup
#: model:ir.model.fields.selection,name:auth_signup.selection__res_config_settings__auth_signup_uninvited__b2c
msgid "Free sign up"
msgstr "免費註冊"

#. module: auth_signup
#: model:ir.model,name:auth_signup.model_ir_http
msgid "HTTP Routing"
msgstr "HTTP 路由"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.reset_password_email
msgid ""
"If you do not expect this, you can safely ignore this email.<br/><br/>\n"
"                                Thanks,"
msgstr ""
"若不是你要求更改密碼，請忽略本電郵。<br/><br/>\n"
"                                謝謝！"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.alert_login_new_device
msgid ""
"If you don't recognize it, you should change your password immediately via "
"this link:<br/>"
msgstr "如果無法識別，請立即通過此連結更改密碼：<br/>"

#. module: auth_signup
#. odoo-python
#: code:addons/auth_signup/models/res_users.py:0
msgid "In %(country)s"
msgstr "在 %(country)s 中"

#. module: auth_signup
#. odoo-python
#: code:addons/auth_signup/controllers/main.py:0
msgid "Invalid signup token"
msgstr "無效的註冊代碼(token)"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.res_config_settings_view_form
msgid "Let your customers log in to see their documents"
msgstr "讓您的客戶登入查看他們的文件"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.alert_login_new_device
#: model_terms:ir.ui.view,arch_db:auth_signup.reset_password_email
msgid "Marc Demo"
msgstr "Marc Demo"

#. module: auth_signup
#. odoo-python
#: code:addons/auth_signup/models/res_users.py:0
msgid "Multiple accounts found for this login"
msgstr "此登入名稱下找到多個帳戶"

#. module: auth_signup
#. odoo-python
#: code:addons/auth_signup/models/res_users.py:0
msgid "Near %(city)s, %(region)s, %(country)s"
msgstr "%(country)s %(region)s %(city)s 附近"

#. module: auth_signup
#. odoo-python
#: code:addons/auth_signup/models/res_users.py:0
msgid "Near %(region)s, %(country)s"
msgstr "%(country)s %(region)s 附近"

#. module: auth_signup
#: model:ir.model.fields.selection,name:auth_signup.selection__res_users__state__new
msgid "Never Connected"
msgstr "從未登入系統"

#. module: auth_signup
#. odoo-python
#: code:addons/auth_signup/models/res_users.py:0
msgid "New Connection to your Account"
msgstr "你的帳戶有新連線"

#. module: auth_signup
#. odoo-python
#: code:addons/auth_signup/models/res_users.py:0
msgid "No account found for this login"
msgstr "找不到此登入名稱的帳戶"

#. module: auth_signup
#. odoo-python
#: code:addons/auth_signup/controllers/main.py:0
msgid "No login provided."
msgstr "未提供登入帳號。"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.alert_login_new_device
msgid "OS"
msgstr "OS（作業系統）"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.alert_login_new_device
#: model_terms:ir.ui.view,arch_db:auth_signup.reset_password_email
msgid "Odoo"
msgstr "Odoo"

#. module: auth_signup
#: model:ir.model.fields.selection,name:auth_signup.selection__res_config_settings__auth_signup_uninvited__b2b
msgid "On invitation"
msgstr "依邀請"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.alert_login_new_device
msgid "Otherwise, you can safely ignore this email."
msgstr "否則，你可以忽略此電郵。"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.fields
msgid "Password"
msgstr "密碼"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.res_config_settings_view_form
msgid "Password Reset"
msgstr "重設密碼"

#. module: auth_signup
#. odoo-python
#: code:addons/auth_signup/models/res_users.py:0
msgid "Password reset"
msgstr "重設密碼"

#. module: auth_signup
#. odoo-python
#: code:addons/auth_signup/controllers/main.py:0
msgid "Password reset instructions sent to your email"
msgstr "重設密碼指示已發送至你的電郵"

#. module: auth_signup
#. odoo-python
#: code:addons/auth_signup/controllers/main.py:0
msgid "Passwords do not match; please retype them."
msgstr "密碼不匹配；請重新輸入密碼。"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.alert_login_new_device
#: model_terms:ir.ui.view,arch_db:auth_signup.reset_password_email
msgid "Powered by"
msgstr "官方技術支援"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.login_successful
msgid "Registration successful."
msgstr "註冊成功。"

#. module: auth_signup
#: model:mail.template,subject:auth_signup.mail_template_data_unregistered_users
msgid "Reminder for unregistered users"
msgstr "未註冊使用者的提醒"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.alert_login_new_device
#: model_terms:ir.ui.view,arch_db:auth_signup.login
#: model_terms:ir.ui.view,arch_db:auth_signup.reset_password
msgid "Reset Password"
msgstr "重設新密碼"

#. module: auth_signup
#: model:ir.actions.server,name:auth_signup.action_send_password_reset_instructions
#: model_terms:ir.ui.view,arch_db:auth_signup.res_users_view_form
msgid "Send Password Reset Instructions"
msgstr "發送重設密碼指示"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.res_users_view_form
msgid "Send an Invitation Email"
msgstr "發送邀請信件"

#. module: auth_signup
#: model:mail.template,description:auth_signup.mail_template_data_unregistered_users
msgid ""
"Sent automatically to admin if new user haven't responded to the invitation"
msgstr "如果新用戶未響應邀請，則自動發送給管理員"

#. module: auth_signup
#: model:mail.template,description:auth_signup.set_password_email
msgid "Sent to new user after you invited them"
msgstr "邀請新用戶後發送給他們"

#. module: auth_signup
#: model:mail.template,description:auth_signup.mail_template_user_signup_account_created
msgid "Sent to portal user who registered themselves"
msgstr "發送給自己註冊的門戶用戶"

#. module: auth_signup
#: model:mail.template,name:auth_signup.mail_template_user_signup_account_created
msgid "Settings: New Portal Sign Up"
msgstr "設定：門戶網站新用戶註冊"

#. module: auth_signup
#: model:mail.template,name:auth_signup.set_password_email
msgid "Settings: New User Invite"
msgstr "設置：新用戶邀請"

#. module: auth_signup
#: model:mail.template,name:auth_signup.mail_template_data_unregistered_users
msgid "Settings: Unregistered User Reminder"
msgstr "設置：未註冊用戶提醒"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.signup
msgid "Sign up"
msgstr "註冊"

#. module: auth_signup
#: model:ir.model.fields,field_description:auth_signup.field_res_partner__signup_type
#: model:ir.model.fields,field_description:auth_signup.field_res_users__signup_type
msgid "Signup Token Type"
msgstr "註冊代碼（Token）類型"

#. module: auth_signup
#. odoo-python
#: code:addons/auth_signup/models/res_users.py:0
msgid "Signup is not allowed for uninvited users"
msgstr "不允許未邀請的使用者進行註冊"

#. module: auth_signup
#. odoo-python
#: code:addons/auth_signup/models/res_partner.py:0
msgid "Signup token '%s' is not valid or expired"
msgstr "註冊權杖「%s」無效或已過期"

#. module: auth_signup
#. odoo-python
#: code:addons/auth_signup/models/res_users.py:0
msgid "Signup: invalid template user"
msgstr "註冊：無效使用者模板"

#. module: auth_signup
#. odoo-python
#: code:addons/auth_signup/models/res_users.py:0
msgid "Signup: no login given for new user"
msgstr "註冊：新使用者未註冊畫面"

#. module: auth_signup
#. odoo-python
#: code:addons/auth_signup/models/res_users.py:0
msgid "Signup: no name or partner given for new user"
msgstr "註冊: 沒有為新使用者提供的名稱或合作夥伴"

#. module: auth_signup
#: model:ir.model.fields,field_description:auth_signup.field_res_users__state
msgid "Status"
msgstr "狀態"

#. module: auth_signup
#. odoo-python
#: code:addons/auth_signup/controllers/main.py:0
msgid "Suspicious activity detected by Google reCaptcha."
msgstr "Google reCaptcha 檢測到的可疑活動."

#. module: auth_signup
#: model:ir.model.fields,field_description:auth_signup.field_res_config_settings__auth_signup_template_user_id
msgid "Template user for new users created through signup"
msgstr "用作透過註冊建立的新使用者的模板"

#. module: auth_signup
#. odoo-python
#: code:addons/auth_signup/controllers/main.py:0
msgid "The form was not properly filled in."
msgstr "未正確填寫表格。"

#. module: auth_signup
#. odoo-python
#: code:addons/auth_signup/models/res_users.py:0
msgid ""
"There was an error when trying to deliver your Email, please check your "
"configuration"
msgstr "嘗試發送你的電子郵件時出現錯誤，請檢查設置"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.res_config_settings_view_form
msgid ""
"To send invitations in B2B mode, open a contact or select several ones in "
"list view and click on 'Portal Access Management' option in the dropdown "
"menu *Action*."
msgstr "要在 B2B 模式下發出邀請，請開啟一個聯絡人或在清單檢視中選取多人，然後在*動作*下拉選單中點擊「入口網站存取管理」選項."

#. module: auth_signup
#: model:ir.model,name:auth_signup.model_res_users
msgid "User"
msgstr "使用者"

#. module: auth_signup
#: model:ir.actions.server,name:auth_signup.ir_cron_auth_signup_send_pending_user_reminder_ir_actions_server
msgid "Users: Notify About Unregistered Users"
msgstr "使用者：通知未註冊使用者"

#. module: auth_signup
#: model:mail.template,subject:auth_signup.mail_template_user_signup_account_created
msgid "Welcome to {{ object.company_id.name }}!"
msgstr "歡迎來到 {{ object.company_id.name }}！"

#. module: auth_signup
#. odoo-python
#: code:addons/auth_signup/models/res_users.py:0
msgid "You cannot perform this action on an archived user."
msgstr "您不能對已存檔的用戶執行此操作。"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.fields
#: model_terms:ir.ui.view,arch_db:auth_signup.reset_password
msgid "Your Email"
msgstr "你的電郵"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.fields
msgid "Your Name"
msgstr "您的姓名"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.reset_password_email
msgid "YourCompany"
msgstr "YourCompany"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.alert_login_new_device
msgid "day, month dd, yyyy - hh:mm:ss (GMT)"
msgstr "day, month dd, yyyy - hh:mm:ss (GMT)"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.fields
msgid "e.g. John Doe"
msgstr "例：陳大文"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.reset_password_email
msgid "hours:<br/>"
msgstr "小時：<br/>"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.reset_password_email
msgid "http://www.example.com"
msgstr "http://www.example.com"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.reset_password_email
msgid "<EMAIL>"
msgstr "<EMAIL>"

#. module: auth_signup
#: model:mail.template,subject:auth_signup.set_password_email
msgid ""
"{{ object.create_uid.name }} from {{ object.company_id.name }} invites you "
"to connect to Odoo"
msgstr ""
"{{ object.create_uid.name }} 來自 {{ object.company_id.name }} 邀請您登入到 Odoo"
