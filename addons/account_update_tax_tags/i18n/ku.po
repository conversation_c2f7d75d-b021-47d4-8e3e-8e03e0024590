# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_update_tax_tags
# 
# Translators:
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 08:39+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: Kurdish (https://app.transifex.com/odoo/teams/41243/ku/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ku\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: account_update_tax_tags
#: model:ir.model.fields,field_description:account_update_tax_tags.field_account_update_tax_tags_wizard__company_id
msgid "Company"
msgstr "کۆمپانیا"

#. module: account_update_tax_tags
#: model:ir.model.fields,field_description:account_update_tax_tags.field_account_update_tax_tags_wizard__create_uid
msgid "Created by"
msgstr "دروستکراوە لەلایەن..."

#. module: account_update_tax_tags
#: model:ir.model.fields,field_description:account_update_tax_tags.field_account_update_tax_tags_wizard__create_date
msgid "Created on"
msgstr "دروستکراوە لە"

#. module: account_update_tax_tags
#: model:ir.model.fields,help:account_update_tax_tags.field_account_update_tax_tags_wizard__date_from
msgid "Date from which journal items will be updated."
msgstr ""

#. module: account_update_tax_tags
#: model_terms:ir.ui.view,arch_db:account_update_tax_tags.view_account_update_tax_tags_wizard_form
msgid "Discard"
msgstr "ڕەتکردنەوە"

#. module: account_update_tax_tags
#: model:ir.model.fields,field_description:account_update_tax_tags.field_account_update_tax_tags_wizard__display_lock_date_warning
msgid "Display Lock Date Warning"
msgstr ""

#. module: account_update_tax_tags
#: model:ir.model.fields,field_description:account_update_tax_tags.field_account_update_tax_tags_wizard__display_name
msgid "Display Name"
msgstr ""

#. module: account_update_tax_tags
#: model:ir.model.fields,field_description:account_update_tax_tags.field_account_update_tax_tags_wizard__id
msgid "ID"
msgstr "ID"

#. module: account_update_tax_tags
#: model:ir.model.fields,field_description:account_update_tax_tags.field_account_update_tax_tags_wizard__write_uid
msgid "Last Updated by"
msgstr "دوایین نوێکردنەوە لەلایەن..."

#. module: account_update_tax_tags
#: model:ir.model.fields,field_description:account_update_tax_tags.field_account_update_tax_tags_wizard__write_date
msgid "Last Updated on"
msgstr "دوایین نوێکردنەوە لە..."

#. module: account_update_tax_tags
#: model:ir.model.fields,field_description:account_update_tax_tags.field_account_update_tax_tags_wizard__date_from
msgid "Starting from"
msgstr ""

#. module: account_update_tax_tags
#: model_terms:ir.ui.view,arch_db:account_update_tax_tags.view_account_update_tax_tags_wizard_form
msgid ""
"The date you chose is violating the tax lock date, do this at your own risk."
msgstr ""

#. module: account_update_tax_tags
#: model_terms:ir.ui.view,arch_db:account_update_tax_tags.view_account_update_tax_tags_wizard_form
msgid "Update"
msgstr ""

#. module: account_update_tax_tags
#: model:ir.model,name:account_update_tax_tags.model_account_update_tax_tags_wizard
msgid "Update Tax Tags Wizard"
msgstr ""

#. module: account_update_tax_tags
#: model:ir.actions.act_window,name:account_update_tax_tags.action_open_wizard
#: model_terms:ir.ui.view,arch_db:account_update_tax_tags.res_config_settings_view_form
msgid "Update tax tags on existing Journal Entries"
msgstr ""

#. module: account_update_tax_tags
#. odoo-python
#: code:addons/account_update_tax_tags/wizard/account_update_tax_tags_wizard.py:0
msgid ""
"Update with children taxes that are child of multiple parents is not "
"supported."
msgstr ""

#. module: account_update_tax_tags
#: model_terms:ir.ui.view,arch_db:account_update_tax_tags.view_account_update_tax_tags_wizard_form
msgid ""
"Updating tax tags on existing Journal Entries is an <b>irreversible</b> action that will impact\n"
"                    your reports.<br/>\n"
"                    It is highly recommended to backup your database beforehand.<br/>\n"
"                    The update will change tax tags on your accounting history, starting from and including selected date,\n"
"                    so that it matches with the current configuration of your taxes.<br/>"
msgstr ""
