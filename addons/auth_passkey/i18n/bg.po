# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* auth_passkey
# 
# Translators:
# <PERSON><PERSON>, 2024
# <PERSON><PERSON>, 2024
# <PERSON><PERSON><PERSON><PERSON>, 2024
# KeyVillage, 2024
# <PERSON><PERSON><PERSON>, 2024
# <PERSON>, 2025
# <PERSON><PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 08:40+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2025\n"
"Language-Team: Bulgarian (https://app.transifex.com/odoo/teams/41243/bg/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: bg\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: auth_passkey
#: model_terms:ir.ui.view,arch_db:auth_passkey.auth_passkey_login
msgid "- or -"
msgstr "- или -"

#. module: auth_passkey
#: model_terms:ir.ui.view,arch_db:auth_passkey.res_users_identitycheck_view_form_passkey
msgid "<strong>Use your passkey to authenticate</strong>"
msgstr ""

#. module: auth_passkey
#: model_terms:ir.ui.view,arch_db:auth_passkey.auth_passkey_users_preferences
msgid "Add Passkey"
msgstr ""

#. module: auth_passkey
#: model:ir.model.fields,field_description:auth_passkey.field_res_users_identitycheck__auth_method
msgid "Auth Method"
msgstr ""

#. module: auth_passkey
#: model:ir.model.fields,field_description:auth_passkey.field_res_users__auth_passkey_key_ids
msgid "Auth Passkey Key"
msgstr ""

#. module: auth_passkey
#: model_terms:ir.ui.view,arch_db:auth_passkey.auth_passkey_key_create_view_form
#: model_terms:ir.ui.view,arch_db:auth_passkey.auth_passkey_key_rename
msgid "Cancel"
msgstr "Отказ"

#. module: auth_passkey
#: model_terms:ir.ui.view,arch_db:auth_passkey.auth_passkey_key_create_view_form
msgid "Create"
msgstr "Създай"

#. module: auth_passkey
#. odoo-python
#: code:addons/auth_passkey/models/res_users.py:0
msgid "Create Passkey"
msgstr ""

#. module: auth_passkey
#: model:ir.actions.act_window,name:auth_passkey.action_auth_passkey_key_create
msgid "Create Passkey Wizard"
msgstr ""

#. module: auth_passkey
#: model:ir.model,name:auth_passkey.model_auth_passkey_key_create
msgid "Create a Passkey"
msgstr ""

#. module: auth_passkey
#: model:ir.model.fields,field_description:auth_passkey.field_auth_passkey_key__create_uid
#: model:ir.model.fields,field_description:auth_passkey.field_auth_passkey_key_create__create_uid
msgid "Created by"
msgstr "Създаден от"

#. module: auth_passkey
#: model:ir.model.fields,field_description:auth_passkey.field_auth_passkey_key__create_date
#: model:ir.model.fields,field_description:auth_passkey.field_auth_passkey_key_create__create_date
msgid "Created on"
msgstr "Създадено на"

#. module: auth_passkey
#: model_terms:ir.ui.view,arch_db:auth_passkey.auth_passkey_users_form
#: model_terms:ir.ui.view,arch_db:auth_passkey.auth_passkey_users_preferences
msgid "Created:"
msgstr "Създадено:"

#. module: auth_passkey
#: model:ir.model.fields,field_description:auth_passkey.field_auth_passkey_key__credential_identifier
msgid "Credential Identifier"
msgstr ""

#. module: auth_passkey
#: model_terms:ir.ui.view,arch_db:auth_passkey.auth_passkey_users_form
#: model_terms:ir.ui.view,arch_db:auth_passkey.auth_passkey_users_preferences
msgid "Delete"
msgstr "Изтрий"

#. module: auth_passkey
#: model:ir.model.fields,field_description:auth_passkey.field_auth_passkey_key__display_name
#: model:ir.model.fields,field_description:auth_passkey.field_auth_passkey_key_create__display_name
msgid "Display Name"
msgstr "Име за показване"

#. module: auth_passkey
#: model:ir.model.fields,field_description:auth_passkey.field_auth_passkey_key__id
#: model:ir.model.fields,field_description:auth_passkey.field_auth_passkey_key_create__id
msgid "ID"
msgstr "ID"

#. module: auth_passkey
#. odoo-python
#: code:addons/auth_passkey/models/res_users_identitycheck.py:0
msgid ""
"Incorrect Passkey. Please provide a valid passkey or use a different "
"authentication method."
msgstr ""

#. module: auth_passkey
#: model:ir.model.fields,field_description:auth_passkey.field_auth_passkey_key__write_uid
#: model:ir.model.fields,field_description:auth_passkey.field_auth_passkey_key_create__write_uid
msgid "Last Updated by"
msgstr "Последно актуализирано от"

#. module: auth_passkey
#: model:ir.model.fields,field_description:auth_passkey.field_auth_passkey_key__write_date
#: model:ir.model.fields,field_description:auth_passkey.field_auth_passkey_key_create__write_date
msgid "Last Updated on"
msgstr "Последно актуализирано на"

#. module: auth_passkey
#: model_terms:ir.ui.view,arch_db:auth_passkey.auth_passkey_users_form
#: model_terms:ir.ui.view,arch_db:auth_passkey.auth_passkey_users_preferences
msgid "Last used:"
msgstr ""

#. module: auth_passkey
#: model_terms:ir.ui.view,arch_db:auth_passkey.auth_passkey_login
msgid "Log in with Passkey"
msgstr ""

#. module: auth_passkey
#: model:ir.model.fields,field_description:auth_passkey.field_auth_passkey_key__name
#: model:ir.model.fields,field_description:auth_passkey.field_auth_passkey_key_create__name
msgid "Name"
msgstr "Име"

#. module: auth_passkey
#: model_terms:ir.ui.view,arch_db:auth_passkey.res_users_identitycheck_view_form_passkey
msgid "Or choose a different method:"
msgstr ""

#. module: auth_passkey
#: model:ir.model,name:auth_passkey.model_auth_passkey_key
#: model:ir.model.fields.selection,name:auth_passkey.selection__res_users_identitycheck__auth_method__webauthn
msgid "Passkey"
msgstr ""

#. module: auth_passkey
#: model_terms:ir.ui.view,arch_db:auth_passkey.auth_passkey_users_form
#: model_terms:ir.ui.view,arch_db:auth_passkey.auth_passkey_users_preferences
msgid "Passkeys"
msgstr ""

#. module: auth_passkey
#: model_terms:ir.ui.view,arch_db:auth_passkey.auth_passkey_users_preferences
msgid ""
"Passkeys are a replacement for your username and password, offering a more "
"secure way of logging in."
msgstr ""

#. module: auth_passkey
#: model:ir.model,name:auth_passkey.model_res_users_identitycheck
msgid "Password Check Wizard"
msgstr ""

#. module: auth_passkey
#: model:ir.model.fields,field_description:auth_passkey.field_auth_passkey_key__public_key
msgid "Public Key"
msgstr ""

#. module: auth_passkey
#: model_terms:ir.ui.view,arch_db:auth_passkey.auth_passkey_users_preferences
msgid "Rename"
msgstr "Преименувай"

#. module: auth_passkey
#. odoo-python
#: code:addons/auth_passkey/models/auth_passkey_key.py:0
msgid "Rename Passkey"
msgstr ""

#. module: auth_passkey
#: model_terms:ir.ui.view,arch_db:auth_passkey.auth_passkey_key_rename
msgid "Save"
msgstr "Запазете"

#. module: auth_passkey
#. odoo-python
#: code:addons/auth_passkey/models/res_users_identitycheck.py:0
msgid "Security Control"
msgstr "Контрол на сигурността"

#. module: auth_passkey
#: model:ir.model.fields,field_description:auth_passkey.field_auth_passkey_key__sign_count
msgid "Sign Count"
msgstr ""

#. module: auth_passkey
#: model:ir.model.constraint,message:auth_passkey.constraint_auth_passkey_key_unique_identifier
msgid "The credential identifier should be unique."
msgstr ""

#. module: auth_passkey
#. odoo-python
#: code:addons/auth_passkey/models/res_users.py:0
msgid "Unknown passkey"
msgstr ""

#. module: auth_passkey
#: model_terms:ir.ui.view,arch_db:auth_passkey.res_users_identitycheck_view_form_passkey
msgid "Use Passkey"
msgstr ""

#. module: auth_passkey
#: model_terms:ir.ui.view,arch_db:auth_passkey.res_users_identitycheck_view_form_passkey
msgid "Use password"
msgstr ""

#. module: auth_passkey
#: model:ir.model,name:auth_passkey.model_res_users
msgid "User"
msgstr "Потребител"
