# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_payment
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON> <george_ta<PERSON><PERSON><PERSON>@yahoo.com>, 2024
# <PERSON>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON><PERSON>, 2025
# <PERSON> Mimou, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-25 07:48+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Anastasia Mimou, 2025\n"
"Language-Team: Greek (https://app.transifex.com/odoo/teams/41243/el/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: el\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/wizards/payment_link_wizard.py:0
msgid ""
"#%(number)s - Installment of <strong>%(amount)s</strong> due on <strong "
"class=\"text-primary\">%(date)s</strong>"
msgstr ""

#. module: account_payment
#. odoo-javascript
#: code:addons/account_payment/static/src/js/portal_my_invoices_payment.js:0
msgid "%s day(s) overdue"
msgstr ""

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_success
msgid "<b>Communication: </b>"
msgstr "<b>Επικοινωνία: </b>"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_my_invoices_payment
msgid ""
"<i class=\"fa fa-arrow-circle-right\"/><span class=\"d-none d-md-inline\"> "
"Pay Now</span>"
msgstr ""
"<i class=\"fa fa-arrow-circle-right\"/><span class=\"d-none d-md-inline\"> "
"Πλήρωσε Τώρα</span>"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_page_inherit_payment
msgid "<i class=\"fa fa-fw fa-arrow-circle-right\"/> Pay Now"
msgstr "<i class=\"fa fa-fw fa-arrow-circle-right\"/> Πλήρωσε Τώρα"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_my_invoices_payment
msgid ""
"<i class=\"fa fa-fw fa-check\"/>\n"
"                <span class=\"d-none d-md-inline\"> Authorized</span>"
msgstr ""
"<i class=\"fa fa-fw fa-check\"/>\n"
"                <span class=\"d-none d-md-inline\">Εξουσιοδοτημένο</span>"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_my_invoices_payment
msgid ""
"<i class=\"fa fa-fw fa-check\"/>\n"
"                <span class=\"d-none d-md-inline\"> Paid</span>"
msgstr ""
"<i class=\"fa fa-fw fa-check\"/>\n"
"                <span class=\"d-none d-md-inline\"> Πληρωμένο</span>"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_page_inherit_payment
msgid "<i class=\"fa fa-fw fa-check-circle\"/> Paid"
msgstr "<i class=\"fa fa-fw fa-check-circle\"/> Πληρωμένο"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_page_inherit_payment
msgid "<i class=\"fa fa-fw fa-check-circle\"/> Pending"
msgstr "<i class=\"fa fa-fw fa-check-circle\"/> Εκκρεμεί"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_page_inherit_payment
msgid "<i class=\"fa fa-fw fa-check-circle\"/> Processing Payment"
msgstr ""

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_my_invoices_payment
msgid "<span class=\"d-none d-md-inline\"> Pending</span>"
msgstr "<span class=\"d-none d-md-inline\"> Εκκρεμεί</span>"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_payment
msgid "<strong>Full Amount</strong><br/>"
msgstr ""

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_payment
msgid ""
"<strong>Installment</strong>\n"
"                                        <br/>"
msgstr ""

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.payment_refund_wizard_view_form
msgid ""
"<strong>Warning!</strong> There is a refund pending for this payment.\n"
"                        Wait a moment for it to be processed. If the refund is still pending in a\n"
"                        few minutes, please check your payment provider configuration."
msgstr ""
"<strong>Προσοχή!</strong> Υπάρχει εκκρεμής επιστροφή χρημάτων για αυτή την πληρωμή.\n"
"                        Περιμένετε λίγο μέχρι να επεξεργαστεί. Αν η επιστροφή χρημάτων είναι ακόμα εκκρεμής σε\n"
"                        λίγα λεπτά, παρακαλώ ελέγξτε τη ρύθμιση του παρόχου πληρωμών σας."

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/wizards/payment_link_wizard.py:0
msgid "A discount will be applied if the customer pays before %s included."
msgstr ""

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_payment
msgid ""
"A payment has already been made on this invoice, please make sure to not pay"
" twice."
msgstr ""

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/models/account_payment.py:0
msgid "A payment transaction with reference %s already exists."
msgstr "Η συναλλαγή πληρωμής με αναφορά %sυπάρχει ήδη. "

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/models/account_payment.py:0
msgid "A token is required to create a new payment transaction."
msgstr ""
"Απαιτείται ένας κωδικός ασφαλείας για να δημιουργηθεί μια νέα συναλλαγή "
"πληρωμής."

#. module: account_payment
#: model:onboarding.onboarding.step,button_text:account_payment.onboarding_onboarding_step_payment_provider
msgid "Activate Stripe"
msgstr "Ενεργοποιήστε το Stripe"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_overdue_invoices_page
msgid "Amount"
msgstr "Ποσό"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_account_payment__amount_available_for_refund
msgid "Amount Available For Refund"
msgstr "Ποσό Διαθέσιμο για Επιστροφή Χρημάτων"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_link_wizard__invoice_amount_due
msgid "Amount Due"
msgstr "Οφειλόμενο Ποσό"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_account_bank_statement_line__amount_paid
#: model:ir.model.fields,field_description:account_payment.field_account_move__amount_paid
msgid "Amount paid"
msgstr "Ποσό που πληρώθηκε"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.account_invoice_view_form_inherit_payment
msgid ""
"Are you sure you want to void the authorized transaction? This action can't "
"be undone."
msgstr ""
"Είστε σίγουροι ότι θέλετε να ακυρώσετε την εξουσιοδοτημένη συναλλαγή; Αυτή η"
" ενέργεια δεν μπορεί να αναιρεθεί."

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_account_bank_statement_line__authorized_transaction_ids
#: model:ir.model.fields,field_description:account_payment.field_account_move__authorized_transaction_ids
msgid "Authorized Transactions"
msgstr "Εξουσιοδοτημένες Συναλλαγές"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.account_invoice_view_form_inherit_payment
msgid "Capture Transaction"
msgstr "Καταγραφή Συναλλαγής"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.payment_refund_wizard_view_form
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_payment_paid
msgid "Close"
msgstr "Κλείσιμο"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_account_payment_register__payment_method_code
msgid "Code"
msgstr "Κωδικός"

#. module: account_payment
#: model:ir.model,name:account_payment.model_res_config_settings
msgid "Config Settings"
msgstr "Ρυθμίσεις διαμόρφωσης"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__create_uid
msgid "Created by"
msgstr "Δημιουργήθηκε από"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__create_date
msgid "Created on"
msgstr "Δημιουργήθηκε στις"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__currency_id
msgid "Currency"
msgstr "Νόμισμα"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_link_wizard__discount_date
msgid "Discount Date"
msgstr "Ημερομηνία Έκπτωσης"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__display_name
msgid "Display Name"
msgstr "Εμφάνιση Ονόματος"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_link_wizard__display_open_installments
msgid "Display Open Installments"
msgstr ""

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_success
msgid ""
"Done, your online payment has been successfully processed. Thank you for "
"your order."
msgstr ""
"Ολοκληρώθηκε, η διαδικτυακή σας πληρωμή έχει επεξεργαστεί επιτυχώς. "
"Ευχαριστούμε για την παραγγελία σας"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_link_wizard__epd_info
msgid "Early Payment Discount Information"
msgstr ""

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_payment
msgid "Early Payment Discount of"
msgstr ""

#. module: account_payment
#: model:onboarding.onboarding.step,description:account_payment.onboarding_onboarding_step_payment_provider
msgid "Enable credit & debit card payments supported by Stripe."
msgstr ""
"Ενεργοποιήστε τις πληρωμές με πιστωτικές και χρεωστικές κάρτες που "
"υποστηρίζονται από το Stripe."

#. module: account_payment
#: model:ir.model.fields.selection,name:account_payment.selection__payment_refund_wizard__support_refund__full_only
msgid "Full Only"
msgstr "Μόνο Πλήρες"

#. module: account_payment
#: model:ir.model,name:account_payment.model_payment_link_wizard
msgid "Generate Sales Payment Link"
msgstr "Δημιουργία Συνδέσμου Πληρωμής Πώλησης"

#. module: account_payment
#: model:ir.actions.act_window,name:account_payment.action_invoice_order_generate_link
msgid "Generate a Payment Link"
msgstr "Δημιουργία Συνδέσμου Πληρωμής"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_link_wizard__has_eligible_epd
msgid "Has Eligible Epd"
msgstr ""

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__has_pending_refund
msgid "Has a pending refund"
msgstr "Έχει εκκρεμή επιστροφή χρημάτων"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__id
msgid "ID"
msgstr "Κωδικός"

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/controllers/payment.py:0
msgid ""
"Impossible to pay all the overdue invoices if they don't share the same "
"currency."
msgstr ""

#. module: account_payment
#: model:ir.model.fields,help:account_payment.field_account_payment_method_line__payment_provider_state
msgid ""
"In test mode, a fake payment is processed through a test payment interface.\n"
"This mode is advised when setting up the provider."
msgstr ""
"Στη λειτουργία δοκιμής, επεξεργάζεται μια ψεύτικη πληρωμή μέσω διεπαφής δοκιμής πληρωμής.\n"
"Αυτή η λειτουργία συνιστάται κατά την ρύθμιση του παρόχου."

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.res_config_settings_view_form
msgid "Invoice Online Payment"
msgstr "Online Πληρωμή Τιμολογίου"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.payment_transaction_form
msgid "Invoice(s)"
msgstr "Τιμολόγιο(α)"

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/models/payment_transaction.py:0
#: model:ir.model.fields,field_description:account_payment.field_payment_transaction__invoice_ids
msgid "Invoices"
msgstr "Τιμολόγια"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_my_home_overdue_invoice
msgid "Invoices &amp; Bills"
msgstr ""

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_transaction__invoices_count
msgid "Invoices Count"
msgstr "Αριθμός Τιμολογίων"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_my_home_account_payment
msgid "Invoices to pay"
msgstr ""

#. module: account_payment
#: model:ir.model,name:account_payment.model_account_journal
msgid "Journal"
msgstr "Ημερολόγιο"

#. module: account_payment
#: model:ir.model,name:account_payment.model_account_move
msgid "Journal Entry"
msgstr "Εγγραφή Ημερολογίου"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__write_uid
msgid "Last Updated by"
msgstr "Τελευταία Ενημέρωση από"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__write_date
msgid "Last Updated on"
msgstr "Τελευταία Ενημέρωση στις"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__amount_available_for_refund
msgid "Maximum Refund Allowed"
msgstr "Μέγιστη Επιτρεπόμενη Επιστροφή Χρημάτων"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.payment_link_wizard__form_inherit_account_payment
msgid "Next Installments"
msgstr ""

#. module: account_payment
#: model:ir.model.fields,help:account_payment.field_account_payment__payment_token_id
msgid ""
"Note that only tokens from providers allowing to capture the amount are "
"available."
msgstr ""
"Σημειώστε ότι είναι διαθέσιμοι μόνο οι κωδικοί ασφαλείας από παρόχους που "
"επιτρέπουν την καταγραφή του ποσού."

#. module: account_payment
#: model:ir.model.fields,help:account_payment.field_account_payment_register__payment_token_id
msgid ""
"Note that tokens from providers set to only authorize transactions (instead "
"of capturing the amount) are not available."
msgstr ""
"Σημειώστε ότι οι κωδικοί ασφαλείας από παρόχους που είναι ρυθμισμένοι να "
"εξουσιοδοτούν μόνο συναλλαγές (αντί να καταγράφουν το ποσό) δεν είναι "
"διαθέσιμοι."

#. module: account_payment
#: model:onboarding.onboarding.step,step_image_alt:account_payment.onboarding_onboarding_step_payment_provider
msgid "Onboarding Online Payments"
msgstr "Εγγραφή για Διαδικτυακές Πληρωμές"

#. module: account_payment
#: model:ir.model,name:account_payment.model_onboarding_onboarding_step
msgid "Onboarding Step"
msgstr "Βήμα Εγγραφής"

#. module: account_payment
#: model:onboarding.onboarding.step,title:account_payment.onboarding_onboarding_step_payment_provider
msgid "Online Payments"
msgstr "Online Πληρωμές"

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/wizards/payment_link_wizard.py:0
msgid "Online payment option is not enabled in Configuration."
msgstr ""

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/controllers/portal.py:0
msgid "Overdue invoices should share the same company."
msgstr ""

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/controllers/portal.py:0
msgid "Overdue invoices should share the same currency."
msgstr ""

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/controllers/portal.py:0
msgid "Overdue invoices should share the same partner."
msgstr ""

#. module: account_payment
#: model:ir.model.fields.selection,name:account_payment.selection__payment_refund_wizard__support_refund__partial
msgid "Partial"
msgstr "Μερική"

#. module: account_payment
#: model:ir.model,name:account_payment.model_account_payment_register
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_payment
msgid "Pay"
msgstr "Πληρωμή"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_payment_paid
msgid "Pay Invoice"
msgstr ""

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_res_config_settings__pay_invoices_online
msgid "Pay Invoices Online"
msgstr "Πληρώστε Τα Τιμολόγια Online"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_docs_entry
#: model_terms:ir.ui.view,arch_db:account_payment.portal_my_invoices_payment
msgid "Pay Now"
msgstr "Εξόφληση Τώρα"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_my_invoices_payment
msgid "Pay now"
msgstr "Πλήρωσε τώρα"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_my_home_overdue_invoice
msgid "Pay overdue"
msgstr ""

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__payment_id
#: model:ir.model.fields,field_description:account_payment.field_payment_transaction__payment_id
msgid "Payment"
msgstr "Πληρωμή"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__payment_amount
msgid "Payment Amount"
msgstr "Ποσό Πληρωμής"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_provider__journal_id
msgid "Payment Journal"
msgstr "Ημερολόγιο Πληρωμών"

#. module: account_payment
#: model:ir.model,name:account_payment.model_account_payment_method
#: model:ir.model,name:account_payment.model_account_payment_method_line
#: model:ir.ui.menu,name:account_payment.payment_method_menu
msgid "Payment Methods"
msgstr "Μέθοδοι Πληρωμής"

#. module: account_payment
#: model:ir.model,name:account_payment.model_payment_provider
#: model:ir.model.fields,field_description:account_payment.field_account_payment_method_line__payment_provider_id
msgid "Payment Provider"
msgstr "Πάροχος Πληρωμών"

#. module: account_payment
#: model:ir.ui.menu,name:account_payment.payment_provider_menu
msgid "Payment Providers"
msgstr "Πάροχοι Πληρωμών"

#. module: account_payment
#: model:ir.model,name:account_payment.model_payment_refund_wizard
msgid "Payment Refund Wizard"
msgstr "Οδηγός Επιστροφής Πληρωμής"

#. module: account_payment
#: model:ir.ui.menu,name:account_payment.payment_token_menu
msgid "Payment Tokens"
msgstr "Διακριτικά Πληρωμής"

#. module: account_payment
#: model:ir.model,name:account_payment.model_payment_transaction
#: model:ir.model.fields,field_description:account_payment.field_account_payment__payment_transaction_id
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__transaction_id
#: model_terms:ir.ui.view,arch_db:account_payment.account_invoice_view_form_inherit_payment
msgid "Payment Transaction"
msgstr "Συναλλαγή Πληρωμής"

#. module: account_payment
#: model:ir.ui.menu,name:account_payment.payment_transaction_menu
msgid "Payment Transactions"
msgstr "Συναλλαγές Πληρωμής"

#. module: account_payment
#: model:ir.model,name:account_payment.model_account_payment
msgid "Payments"
msgstr "Συναλλαγές"

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/controllers/payment.py:0
msgid "Please log in to pay your overdue invoices"
msgstr ""

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/models/account_payment_method_line.py:0
msgid "Provider"
msgstr "Πάροχος"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_overdue_invoices_page
msgid "Reference"
msgstr "Σχετικό"

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/models/account_payment.py:0
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__support_refund
#: model_terms:ir.ui.view,arch_db:account_payment.payment_refund_wizard_view_form
#: model_terms:ir.ui.view,arch_db:account_payment.view_account_payment_form_inherit_payment
msgid "Refund"
msgstr "Επιστροφή"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__amount_to_refund
msgid "Refund Amount"
msgstr "Ποσό Επιστροφής"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__refunded_amount
msgid "Refunded Amount"
msgstr "Επιστρεφόμενο Ποσό"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.view_account_payment_form_inherit_payment
msgid "Refunds"
msgstr "Επιστροφές"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_account_payment__refunds_count
msgid "Refunds Count"
msgstr "Αριθμός Επιστροφών"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.view_account_journal_form
msgid "SETUP"
msgstr "Διαμόρφωση"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_account_payment__payment_token_id
msgid "Saved Payment Token"
msgstr "Αποθηκευμένος Κωδικός Ασφαλείας Πληρωμής"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_account_payment_register__payment_token_id
msgid "Saved payment token"
msgstr "Αποθηκευμένο διακριτικό πληρωμής"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_account_payment__source_payment_id
msgid "Source Payment"
msgstr "Πηγή Πληρωμής"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_account_payment_method_line__payment_provider_state
msgid "State"
msgstr "Νομός/Πολιτεία"

#. module: account_payment
#: model:onboarding.onboarding.step,done_text:account_payment.onboarding_onboarding_step_payment_provider
msgid "Step Completed!"
msgstr "Βήμα Ολοκληρωμένο!"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_account_payment__suitable_payment_token_ids
#: model:ir.model.fields,field_description:account_payment.field_account_payment_register__suitable_payment_token_ids
msgid "Suitable Payment Token"
msgstr "Κατάλληλος Κωδικός Ασφαλείας Πληρωμής"

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/controllers/payment.py:0
msgid "The access token is invalid."
msgstr "Ο κωδικός ασφαλείας πρόσβασης είναι άκυρος."

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/wizards/payment_refund_wizard.py:0
msgid ""
"The amount to be refunded must be positive and cannot be superior to %s."
msgstr ""
"Το ποσό που πρέπει να επιστραφεί πρέπει να είναι θετικό και δεν μπορεί να "
"υπερβαίνει το %s."

#. module: account_payment
#: model:ir.model.fields,help:account_payment.field_payment_provider__journal_id
msgid "The journal in which the successful transactions are posted."
msgstr "Το ημερολόγιο στο οποίο καταχωρούνται οι επιτυχείς συναλλαγές."

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/models/payment_transaction.py:0
msgid ""
"The payment related to the transaction with reference %(ref)s has been "
"posted: %(link)s"
msgstr ""
"Η πληρωμή που σχετίζεται με τη συναλλαγή με αναφορά %(ref)s έχει "
"καταχωρηθεί: %(link)s"

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/controllers/payment.py:0
msgid "The provided parameters are invalid."
msgstr "Οι παρεχόμενες παράμετροι είναι άκυρες."

#. module: account_payment
#: model:ir.model.fields,help:account_payment.field_account_payment__source_payment_id
msgid "The source payment of related refund payments"
msgstr "Η πηγή πληρωμής των σχετικών πληρωμών επιστροφής χρημάτων"

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/models/account_move.py:0
msgid "There are pending transactions for this invoice."
msgstr ""

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/models/account_move.py:0
msgid "There is no amount to be paid."
msgstr ""

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_error
msgid "There was an error processing your payment: invalid invoice."
msgstr ""
"Παρουσιάστηκε σφάλμα κατά την επεξεργασία της πληρωμής σας: μη έγκυρο "
"τιμολόγιο."

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_error
msgid ""
"There was an error processing your payment: issue with credit card ID "
"validation."
msgstr ""
"Παρουσιάστηκε σφάλμα κατά την επεξεργασία της πληρωμής σας:  πρόβλημα με την"
" επικύρωση ταυτότητας της πιστωτικής κάρτας."

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_error
msgid "There was an error processing your payment: transaction failed.<br/>"
msgstr ""
"Παρουσιάστηκε σφάλμα κατά την επεξεργασία της πληρωμής σας: απέτυχε η "
"συναλλαγή.<br/>"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_error
msgid "There was en error processing your payment: invalid credit card ID."
msgstr ""
"Παρουσιάστηκε σφάλμα κατά την επεξεργασία της πληρωμής σας: μη έγκυρη "
"ταυτότητα πιστωτικής κάρτας."

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/models/account_move.py:0
msgid "This invoice cannot be paid online."
msgstr ""

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/models/account_move.py:0
msgid "This invoice has already been paid."
msgstr ""

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/models/account_move.py:0
msgid "This invoice isn't posted."
msgstr ""

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/models/account_move.py:0
msgid "This is not an outgoing invoice."
msgstr ""

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_account_bank_statement_line__transaction_count
#: model:ir.model.fields,field_description:account_payment.field_account_move__transaction_count
msgid "Transaction Count"
msgstr ""

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_account_bank_statement_line__transaction_ids
#: model:ir.model.fields,field_description:account_payment.field_account_move__transaction_ids
msgid "Transactions"
msgstr "Συναλλαγές"

#. module: account_payment
#: model:ir.model.fields.selection,name:account_payment.selection__payment_refund_wizard__support_refund__none
msgid "Unsupported"
msgstr ""

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_account_payment__use_electronic_payment_method
#: model:ir.model.fields,field_description:account_payment.field_account_payment_register__use_electronic_payment_method
msgid "Use Electronic Payment Method"
msgstr "Χρησιμοποιήστε Ηλεκτρονική Μέθοδο Πληρωμής"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.account_invoice_view_form_inherit_payment
msgid "Void Transaction"
msgstr "Άκυρη συναλλαγή"

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/models/account_payment_method_line.py:0
msgid ""
"You can't delete a payment method that is linked to a provider in the enabled or test state.\n"
"Linked providers(s): %s"
msgstr ""
"Δεν μπορείτε να διαγράψετε μια μέθοδο πληρωμής που είναι συνδεδεμένη με έναν"
" πάροχο στην ενεργοποιημένη ή δοκιμαστική κατάσταση. Συνδεδεμένος "
"πάροχος(οι): %s"

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/models/payment_provider.py:0
msgid ""
"You cannot uninstall this module as payments using this payment method "
"already exist."
msgstr ""
"Δεν μπορείτε να απεγκαταστήσετε αυτό το module καθώς υπάρχουν ήδη πληρωμές "
"που χρησιμοποιούν αυτήν τη μέθοδο πληρωμής."

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/models/account_journal.py:0
msgid ""
"You must first deactivate a payment provider before deleting its journal.\n"
"Linked providers: %s"
msgstr ""
"Πρέπει πρώτα να απενεργοποιήσετε έναν πάροχο πληρωμών πριν διαγράψετε το ημερολόγιο του.\n"
"Συνδεδεμένοι πάροχοι: %s"

#. module: account_payment
#. odoo-javascript
#: code:addons/account_payment/static/src/js/portal_my_invoices_payment.js:0
msgid "due in %s day(s)"
msgstr ""

#. module: account_payment
#. odoo-javascript
#: code:addons/account_payment/static/src/js/portal_my_invoices_payment.js:0
msgid "due today"
msgstr ""

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_payment
msgid "has been applied."
msgstr ""

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_my_invoices_payment
msgid "overdue"
msgstr ""
