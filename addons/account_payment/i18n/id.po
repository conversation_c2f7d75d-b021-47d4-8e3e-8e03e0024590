# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_payment
# 
# Translators:
# Wil Odoo, 2024
# <PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-25 07:48+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Abe Manyo, 2025\n"
"Language-Team: Indonesian (https://app.transifex.com/odoo/teams/41243/id/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: id\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/wizards/payment_link_wizard.py:0
msgid ""
"#%(number)s - Installment of <strong>%(amount)s</strong> due on <strong "
"class=\"text-primary\">%(date)s</strong>"
msgstr ""
"#%(number)s - Cicilan sejumlah <strong>%(amount)s</strong> jatuh tempo pada "
"<strong class=\"text-primary\">%(date)s</strong>"

#. module: account_payment
#. odoo-javascript
#: code:addons/account_payment/static/src/js/portal_my_invoices_payment.js:0
msgid "%s day(s) overdue"
msgstr "%s hari terlambat"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_success
msgid "<b>Communication: </b>"
msgstr "<b>Komunikasi: </b>"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_my_invoices_payment
msgid ""
"<i class=\"fa fa-arrow-circle-right\"/><span class=\"d-none d-md-inline\"> "
"Pay Now</span>"
msgstr ""
"<i class=\"fa fa-arrow-circle-right\"/><span class=\"d-none d-md-inline\"> "
"Bayar Sekarang</span>"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_page_inherit_payment
msgid "<i class=\"fa fa-fw fa-arrow-circle-right\"/> Pay Now"
msgstr "<i class=\"fa fa-fw fa-arrow-circle-right\"/> Bayar Sekarang"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_my_invoices_payment
msgid ""
"<i class=\"fa fa-fw fa-check\"/>\n"
"                <span class=\"d-none d-md-inline\"> Authorized</span>"
msgstr ""
"<i class=\"fa fa-fw fa-check\"/>\n"
"                <span class=\"d-none d-md-inline\"> Authorized</span>"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_my_invoices_payment
msgid ""
"<i class=\"fa fa-fw fa-check\"/>\n"
"                <span class=\"d-none d-md-inline\"> Paid</span>"
msgstr ""
"<i class=\"fa fa-fw fa-check\"/>\n"
"                <span class=\"d-none d-md-inline\"> Dibayar</span>"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_page_inherit_payment
msgid "<i class=\"fa fa-fw fa-check-circle\"/> Paid"
msgstr "<i class=\"fa fa-fw fa-check-circle\"/> Dibayar"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_page_inherit_payment
msgid "<i class=\"fa fa-fw fa-check-circle\"/> Pending"
msgstr "<i class=\"fa fa-fw fa-check-circle\"/> Pending"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_page_inherit_payment
msgid "<i class=\"fa fa-fw fa-check-circle\"/> Processing Payment"
msgstr "<i class=\"fa fa-fw fa-check-circle\"/> Memproses Pembayaran"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_my_invoices_payment
msgid "<span class=\"d-none d-md-inline\"> Pending</span>"
msgstr "<span class=\"d-none d-md-inline\"> Pending</span>"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_payment
msgid "<strong>Full Amount</strong><br/>"
msgstr "<strong>Jumlah Lengkap</strong><br/>"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_payment
msgid ""
"<strong>Installment</strong>\n"
"                                        <br/>"
msgstr ""
"<strong>Cicilan</strong>\n"
"                                        <br/>"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.payment_refund_wizard_view_form
msgid ""
"<strong>Warning!</strong> There is a refund pending for this payment.\n"
"                        Wait a moment for it to be processed. If the refund is still pending in a\n"
"                        few minutes, please check your payment provider configuration."
msgstr ""
"<strong>Warning!</strong> Ada refund yang pending untuk pembayaran ini.\n"
"                        Tunggu sebentar sampai refund diproses. Bila refund masih pending setelah\n"
"                        beberapa menit, silakan periksa kembali konfigurasi penyedia pembayaran Anda."

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/wizards/payment_link_wizard.py:0
msgid "A discount will be applied if the customer pays before %s included."
msgstr ""
"Diskon akan diterapkan bila pelanggan membayar sebelum dan termasuk %s. "

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_payment
msgid ""
"A payment has already been made on this invoice, please make sure to not pay"
" twice."
msgstr ""
"Pembayaran sudah dilakukan pada faktur ini, pastikan pembayaran tidak "
"dilakukan dua kali."

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/models/account_payment.py:0
msgid "A payment transaction with reference %s already exists."
msgstr "Sudah ada transaksi pembayaran dengan referensi %s."

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/models/account_payment.py:0
msgid "A token is required to create a new payment transaction."
msgstr "Token dibutuhkan untuk membuat transaksi pembayaran baru."

#. module: account_payment
#: model:onboarding.onboarding.step,button_text:account_payment.onboarding_onboarding_step_payment_provider
msgid "Activate Stripe"
msgstr "Aktifkan Stripe"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_overdue_invoices_page
msgid "Amount"
msgstr "Jumlah"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_account_payment__amount_available_for_refund
msgid "Amount Available For Refund"
msgstr "Jumlah yang Tersedia Untuk Refund"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_link_wizard__invoice_amount_due
msgid "Amount Due"
msgstr "Jumlah Belum Dibayar"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_account_bank_statement_line__amount_paid
#: model:ir.model.fields,field_description:account_payment.field_account_move__amount_paid
msgid "Amount paid"
msgstr "Jumlah yang dibayar"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.account_invoice_view_form_inherit_payment
msgid ""
"Are you sure you want to void the authorized transaction? This action can't "
"be undone."
msgstr ""
"Apakah Anda yakin ingin membatalkan transaksi yang sudah diotorisasi? "
"Tindakan ini tidak dapat dibatalkan."

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_account_bank_statement_line__authorized_transaction_ids
#: model:ir.model.fields,field_description:account_payment.field_account_move__authorized_transaction_ids
msgid "Authorized Transactions"
msgstr "Transaksi Diotorisasi"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.account_invoice_view_form_inherit_payment
msgid "Capture Transaction"
msgstr "Cetak Transaksi"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.payment_refund_wizard_view_form
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_payment_paid
msgid "Close"
msgstr "Tutup"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_account_payment_register__payment_method_code
msgid "Code"
msgstr "Kode"

#. module: account_payment
#: model:ir.model,name:account_payment.model_res_config_settings
msgid "Config Settings"
msgstr "Pengaturan Konfigurasi"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__create_uid
msgid "Created by"
msgstr "Dibuat oleh"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__create_date
msgid "Created on"
msgstr "Dibuat pada"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__currency_id
msgid "Currency"
msgstr "Mata Uang"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_link_wizard__discount_date
msgid "Discount Date"
msgstr "Tanggal Diskon"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__display_name
msgid "Display Name"
msgstr "Nama Tampilan"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_link_wizard__display_open_installments
msgid "Display Open Installments"
msgstr "Display Open Installments"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_success
msgid ""
"Done, your online payment has been successfully processed. Thank you for "
"your order."
msgstr ""
"Selesai, pembayaran online Anda berhasil diproses. Terima kasih untuk "
"pesanan Anda."

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_link_wizard__epd_info
msgid "Early Payment Discount Information"
msgstr "Early Payment Discount Information"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_payment
msgid "Early Payment Discount of"
msgstr "Diskon Pembayaran Awal sebesar"

#. module: account_payment
#: model:onboarding.onboarding.step,description:account_payment.onboarding_onboarding_step_payment_provider
msgid "Enable credit & debit card payments supported by Stripe."
msgstr "Aktifkan pembayaran kartu kredit & debit yang didukung oleh Stripe."

#. module: account_payment
#: model:ir.model.fields.selection,name:account_payment.selection__payment_refund_wizard__support_refund__full_only
msgid "Full Only"
msgstr "Hanya Penuh"

#. module: account_payment
#: model:ir.model,name:account_payment.model_payment_link_wizard
msgid "Generate Sales Payment Link"
msgstr "Buat Link Pembayaran Penjualan"

#. module: account_payment
#: model:ir.actions.act_window,name:account_payment.action_invoice_order_generate_link
msgid "Generate a Payment Link"
msgstr "Buat Link Pembayaran"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_link_wizard__has_eligible_epd
msgid "Has Eligible Epd"
msgstr "Has Eligible Epd"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__has_pending_refund
msgid "Has a pending refund"
msgstr "Memiliki refund yang pending"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__id
msgid "ID"
msgstr "ID"

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/controllers/payment.py:0
msgid ""
"Impossible to pay all the overdue invoices if they don't share the same "
"currency."
msgstr ""
"Mustahil untuk membayar semua faktur yang terlambat bila mereka tidak "
"memiliki mata uang yang sama."

#. module: account_payment
#: model:ir.model.fields,help:account_payment.field_account_payment_method_line__payment_provider_state
msgid ""
"In test mode, a fake payment is processed through a test payment interface.\n"
"This mode is advised when setting up the provider."
msgstr ""
"Pada mode testing, pembayaran palsu akan diproses melalui antarmuka pembayaran testing.\n"
"Mode ini disarankan saat sedang set up provider."

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.res_config_settings_view_form
msgid "Invoice Online Payment"
msgstr "Invoice Online Payment"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.payment_transaction_form
msgid "Invoice(s)"
msgstr "Faktur"

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/models/payment_transaction.py:0
#: model:ir.model.fields,field_description:account_payment.field_payment_transaction__invoice_ids
msgid "Invoices"
msgstr "Faktur"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_my_home_overdue_invoice
msgid "Invoices &amp; Bills"
msgstr "Invoices &amp; Bills"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_transaction__invoices_count
msgid "Invoices Count"
msgstr "Jumlah Faktur"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_my_home_account_payment
msgid "Invoices to pay"
msgstr "Faktur untuk dibayar"

#. module: account_payment
#: model:ir.model,name:account_payment.model_account_journal
msgid "Journal"
msgstr "Jurnal"

#. module: account_payment
#: model:ir.model,name:account_payment.model_account_move
msgid "Journal Entry"
msgstr "Entri Jurnal"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__write_uid
msgid "Last Updated by"
msgstr "Terakhir Diperbarui oleh"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__write_date
msgid "Last Updated on"
msgstr "Terakhir Diperbarui pada"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__amount_available_for_refund
msgid "Maximum Refund Allowed"
msgstr "Refund Maksimum yang Diperbolehkan"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.payment_link_wizard__form_inherit_account_payment
msgid "Next Installments"
msgstr "Cicilan Berikutnya"

#. module: account_payment
#: model:ir.model.fields,help:account_payment.field_account_payment__payment_token_id
msgid ""
"Note that only tokens from providers allowing to capture the amount are "
"available."
msgstr ""
"Mohon diingat bahwa hanya token dari provider yang memungkinkan pengambilan "
"jumlah yang akan tersedia."

#. module: account_payment
#: model:ir.model.fields,help:account_payment.field_account_payment_register__payment_token_id
msgid ""
"Note that tokens from providers set to only authorize transactions (instead "
"of capturing the amount) are not available."
msgstr ""
"Mohon diingat bahwa tidak ada token dari provier yang disetel untuk hanya "
"mengotorisasi transaksi (alih-alih untuk mengambil jumlah)."

#. module: account_payment
#: model:onboarding.onboarding.step,step_image_alt:account_payment.onboarding_onboarding_step_payment_provider
msgid "Onboarding Online Payments"
msgstr "Onboarding Pembayaran Online"

#. module: account_payment
#: model:ir.model,name:account_payment.model_onboarding_onboarding_step
msgid "Onboarding Step"
msgstr "Langkah Onboarding"

#. module: account_payment
#: model:onboarding.onboarding.step,title:account_payment.onboarding_onboarding_step_payment_provider
msgid "Online Payments"
msgstr "Pembayaran Online"

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/wizards/payment_link_wizard.py:0
msgid "Online payment option is not enabled in Configuration."
msgstr "Opsi pembayaran online tidak diaktifkan di Konfigurasi."

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/controllers/portal.py:0
msgid "Overdue invoices should share the same company."
msgstr "Faktur-faktur terlambat harus memiliki perusahaan yang sama."

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/controllers/portal.py:0
msgid "Overdue invoices should share the same currency."
msgstr "Faktur-faktur yang terlambat harus memiliki mata uang yang sama."

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/controllers/portal.py:0
msgid "Overdue invoices should share the same partner."
msgstr "Faktur-faktur yang terlambat harus memiliki partner yang sama."

#. module: account_payment
#: model:ir.model.fields.selection,name:account_payment.selection__payment_refund_wizard__support_refund__partial
msgid "Partial"
msgstr "Parsial"

#. module: account_payment
#: model:ir.model,name:account_payment.model_account_payment_register
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_payment
msgid "Pay"
msgstr "Bayar"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_payment_paid
msgid "Pay Invoice"
msgstr "Bayar Faktur"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_res_config_settings__pay_invoices_online
msgid "Pay Invoices Online"
msgstr "Bayar Faktur Online"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_docs_entry
#: model_terms:ir.ui.view,arch_db:account_payment.portal_my_invoices_payment
msgid "Pay Now"
msgstr "Bayar sekarang"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_my_invoices_payment
msgid "Pay now"
msgstr "Bayar sekarang"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_my_home_overdue_invoice
msgid "Pay overdue"
msgstr "Bayar yang terlambat"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__payment_id
#: model:ir.model.fields,field_description:account_payment.field_payment_transaction__payment_id
msgid "Payment"
msgstr "Pembayaran"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__payment_amount
msgid "Payment Amount"
msgstr "Jumlah Pembayaran"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_provider__journal_id
msgid "Payment Journal"
msgstr "Jurnal Pembayaran"

#. module: account_payment
#: model:ir.model,name:account_payment.model_account_payment_method
#: model:ir.model,name:account_payment.model_account_payment_method_line
#: model:ir.ui.menu,name:account_payment.payment_method_menu
msgid "Payment Methods"
msgstr "Metode-Metode Pembayaran"

#. module: account_payment
#: model:ir.model,name:account_payment.model_payment_provider
#: model:ir.model.fields,field_description:account_payment.field_account_payment_method_line__payment_provider_id
msgid "Payment Provider"
msgstr "Penyedia Pembayaran"

#. module: account_payment
#: model:ir.ui.menu,name:account_payment.payment_provider_menu
msgid "Payment Providers"
msgstr "Penyedia Pembayaran"

#. module: account_payment
#: model:ir.model,name:account_payment.model_payment_refund_wizard
msgid "Payment Refund Wizard"
msgstr "Wizard Refund Pembayaran"

#. module: account_payment
#: model:ir.ui.menu,name:account_payment.payment_token_menu
msgid "Payment Tokens"
msgstr "Token Pembayaran"

#. module: account_payment
#: model:ir.model,name:account_payment.model_payment_transaction
#: model:ir.model.fields,field_description:account_payment.field_account_payment__payment_transaction_id
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__transaction_id
#: model_terms:ir.ui.view,arch_db:account_payment.account_invoice_view_form_inherit_payment
msgid "Payment Transaction"
msgstr "Transaksi Tagihan"

#. module: account_payment
#: model:ir.ui.menu,name:account_payment.payment_transaction_menu
msgid "Payment Transactions"
msgstr "Transaksi Tagihan"

#. module: account_payment
#: model:ir.model,name:account_payment.model_account_payment
msgid "Payments"
msgstr "Pembayaran"

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/controllers/payment.py:0
msgid "Please log in to pay your overdue invoices"
msgstr "Silakan login untuk membayar faktur Anda yang terlambat"

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/models/account_payment_method_line.py:0
msgid "Provider"
msgstr "Pemberi"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_overdue_invoices_page
msgid "Reference"
msgstr "Referensi"

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/models/account_payment.py:0
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__support_refund
#: model_terms:ir.ui.view,arch_db:account_payment.payment_refund_wizard_view_form
#: model_terms:ir.ui.view,arch_db:account_payment.view_account_payment_form_inherit_payment
msgid "Refund"
msgstr "Pengembalian Dana"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__amount_to_refund
msgid "Refund Amount"
msgstr "Jumlah Refund"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__refunded_amount
msgid "Refunded Amount"
msgstr "Jumlah yang Di-refund"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.view_account_payment_form_inherit_payment
msgid "Refunds"
msgstr "Pengembalian"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_account_payment__refunds_count
msgid "Refunds Count"
msgstr "Perhitungan Refund"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.view_account_journal_form
msgid "SETUP"
msgstr "SETUP"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_account_payment__payment_token_id
msgid "Saved Payment Token"
msgstr "Token Pembayaran yang Di-save"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_account_payment_register__payment_token_id
msgid "Saved payment token"
msgstr "Token pembayaran yang di-save"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_account_payment__source_payment_id
msgid "Source Payment"
msgstr "Sumber Pembayaran"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_account_payment_method_line__payment_provider_state
msgid "State"
msgstr "Status"

#. module: account_payment
#: model:onboarding.onboarding.step,done_text:account_payment.onboarding_onboarding_step_payment_provider
msgid "Step Completed!"
msgstr "Langkah Selesai!"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_account_payment__suitable_payment_token_ids
#: model:ir.model.fields,field_description:account_payment.field_account_payment_register__suitable_payment_token_ids
msgid "Suitable Payment Token"
msgstr "Token Pembayaran Cocok"

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/controllers/payment.py:0
msgid "The access token is invalid."
msgstr "Token akses tidak valid."

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/wizards/payment_refund_wizard.py:0
msgid ""
"The amount to be refunded must be positive and cannot be superior to %s."
msgstr ""
"Jumlah yang akan di-refund harus positif dan tidak boleh lebih dari %s."

#. module: account_payment
#: model:ir.model.fields,help:account_payment.field_payment_provider__journal_id
msgid "The journal in which the successful transactions are posted."
msgstr "Jurnal di mana transaksi sukses ditulis."

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/models/payment_transaction.py:0
msgid ""
"The payment related to the transaction with reference %(ref)s has been "
"posted: %(link)s"
msgstr ""
"Pembayaran terkait transaksi dengan referensi %(ref)s telah diposting: "
"%(link)s"

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/controllers/payment.py:0
msgid "The provided parameters are invalid."
msgstr "Parameter yang disediakan tidak valid."

#. module: account_payment
#: model:ir.model.fields,help:account_payment.field_account_payment__source_payment_id
msgid "The source payment of related refund payments"
msgstr "Sumber pembayaran yang terkait pembayaran refund"

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/models/account_move.py:0
msgid "There are pending transactions for this invoice."
msgstr "Terdapat transaksi pending untuk faktur ini."

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/models/account_move.py:0
msgid "There is no amount to be paid."
msgstr "Tidak ada yang perlu dibayar."

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_error
msgid "There was an error processing your payment: invalid invoice."
msgstr "Terjadi kesalahan ketika memproses tagihan Anda: faktur tidak valid."

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_error
msgid ""
"There was an error processing your payment: issue with credit card ID "
"validation."
msgstr ""
"Terjadi kesalahan ketika memproses tagihan Anda: ada masalah pada pengesahan"
" ID kartu kredit."

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_error
msgid "There was an error processing your payment: transaction failed.<br/>"
msgstr "Terjadi kesalahan ketika memproses tagihan Anda: transaksi gagal."

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_error
msgid "There was en error processing your payment: invalid credit card ID."
msgstr ""
"Terjadi kesalahan ketika memproses tagihan Anda: ID kartu kredit tidak "
"valid."

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/models/account_move.py:0
msgid "This invoice cannot be paid online."
msgstr "Faktur ini tidak dapat dibayar online."

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/models/account_move.py:0
msgid "This invoice has already been paid."
msgstr "Faktur ini sudah dibayar."

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/models/account_move.py:0
msgid "This invoice isn't posted."
msgstr "Faktur ini belum dipost."

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/models/account_move.py:0
msgid "This is not an outgoing invoice."
msgstr "Ini bukan faktur keluar."

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_account_bank_statement_line__transaction_count
#: model:ir.model.fields,field_description:account_payment.field_account_move__transaction_count
msgid "Transaction Count"
msgstr "Jumlah Transaksi"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_account_bank_statement_line__transaction_ids
#: model:ir.model.fields,field_description:account_payment.field_account_move__transaction_ids
msgid "Transactions"
msgstr "Transaksi"

#. module: account_payment
#: model:ir.model.fields.selection,name:account_payment.selection__payment_refund_wizard__support_refund__none
msgid "Unsupported"
msgstr "Tidak didukung"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_account_payment__use_electronic_payment_method
#: model:ir.model.fields,field_description:account_payment.field_account_payment_register__use_electronic_payment_method
msgid "Use Electronic Payment Method"
msgstr "Gunakan Metode Pembayaran Elektronik"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.account_invoice_view_form_inherit_payment
msgid "Void Transaction"
msgstr "Batalkan Transaksi"

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/models/account_payment_method_line.py:0
msgid ""
"You can't delete a payment method that is linked to a provider in the enabled or test state.\n"
"Linked providers(s): %s"
msgstr ""
"Anda tidak dapat menghapus metode pembayaran yang terkait provider dalam status enabled atau testing.\n"
"Provider yang terhubung: %s"

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/models/payment_provider.py:0
msgid ""
"You cannot uninstall this module as payments using this payment method "
"already exist."
msgstr ""
"Anda tidak dapat menguninstal modul ini karena pembayaran menggunakan metode"
" pembayaran ini sudah tersedia."

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/models/account_journal.py:0
msgid ""
"You must first deactivate a payment provider before deleting its journal.\n"
"Linked providers: %s"
msgstr ""
"Anda harus pertama-tama menonaktifkan penyedia pembayaran sebelum menghapusnya dari jurnal.\n"
"Provider yang terhubung: %s"

#. module: account_payment
#. odoo-javascript
#: code:addons/account_payment/static/src/js/portal_my_invoices_payment.js:0
msgid "due in %s day(s)"
msgstr "jatuh tempo dalam %s hari"

#. module: account_payment
#. odoo-javascript
#: code:addons/account_payment/static/src/js/portal_my_invoices_payment.js:0
msgid "due today"
msgstr "jatuh tempo hari ini"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_payment
msgid "has been applied."
msgstr "telah diterapkan."

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_my_invoices_payment
msgid "overdue"
msgstr "terlambat"
