# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* auth_oauth
# 
# Translators:
# da<PERSON>, 2024
# <AUTHOR> <EMAIL>, 2024
# NoaFarkash, 2024
# <PERSON><PERSON>, 2024
# <PERSON>, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# ya<PERSON> terner, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <AUTHOR> <EMAIL>, 2024
# or balmas, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 08:39+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: or balmas, 2025\n"
"Language-Team: Hebrew (https://app.transifex.com/odoo/teams/41243/he/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: he\n"
"Plural-Forms: nplurals=3; plural=(n == 1 && n % 1 == 0) ? 0 : (n == 2 && n % 1 == 0) ? 1: 2;\n"

#. module: auth_oauth
#: model_terms:ir.ui.view,arch_db:auth_oauth.providers
msgid "- or -"
msgstr "- או -"

#. module: auth_oauth
#. odoo-python
#: code:addons/auth_oauth/controllers/main.py:0
msgid "Access Denied"
msgstr "הגישה נדחתה"

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_res_config_settings__auth_oauth_google_enabled
msgid "Allow users to sign in with Google"
msgstr "לאפשר למשתמשים להתחבר עם חשבון גוגל"

#. module: auth_oauth
#: model_terms:ir.ui.view,arch_db:auth_oauth.res_config_settings_view_form
msgid "Allow users to sign in with their Google account"
msgstr "אפשר למשתמשים להתחבר עם חשבון הגוגל שלהם"

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_auth_oauth_provider__enabled
msgid "Allowed"
msgstr "מותר"

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_auth_oauth_provider__auth_endpoint
msgid "Authorization URL"
msgstr "כתובת URL לאישור"

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_auth_oauth_provider__css_class
msgid "CSS class"
msgstr ""

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_auth_oauth_provider__client_id
#: model:ir.model.fields,field_description:auth_oauth.field_res_config_settings__auth_oauth_google_client_id
msgid "Client ID"
msgstr "מזהה לקוח"

#. module: auth_oauth
#: model_terms:ir.ui.view,arch_db:auth_oauth.res_config_settings_view_form
msgid "Client ID:"
msgstr "מספר לקוח:"

#. module: auth_oauth
#: model:ir.model,name:auth_oauth.model_res_config_settings
msgid "Config Settings"
msgstr "הגדרות תצורה"

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_auth_oauth_provider__create_uid
msgid "Created by"
msgstr "נוצר על-ידי"

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_auth_oauth_provider__create_date
msgid "Created on"
msgstr "נוצר ב-"

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_auth_oauth_provider__data_endpoint
msgid "Data Endpoint"
msgstr ""

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_auth_oauth_provider__display_name
msgid "Display Name"
msgstr "שם לתצוגה"

#. module: auth_oauth
#: model_terms:ir.ui.view,arch_db:auth_oauth.res_config_settings_view_form
msgid "Google Authentication"
msgstr "אימות גוגל"

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_auth_oauth_provider__id
msgid "ID"
msgstr "מזהה"

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_auth_oauth_provider__write_uid
msgid "Last Updated by"
msgstr "עודכן לאחרונה על-ידי"

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_auth_oauth_provider__write_date
msgid "Last Updated on"
msgstr "עדכון אחרון ב"

#. module: auth_oauth
#: model:ir.model.fields,help:auth_oauth.field_auth_oauth_provider__body
msgid "Link text in Login Dialog"
msgstr ""

#. module: auth_oauth
#: model:auth.oauth.provider,body:auth_oauth.provider_facebook
msgid "Log in with Facebook"
msgstr "התחברות עם פייסבוק"

#. module: auth_oauth
#: model:auth.oauth.provider,body:auth_oauth.provider_google
msgid "Log in with Google"
msgstr "התחברות עם גוגל"

#. module: auth_oauth
#: model:auth.oauth.provider,body:auth_oauth.provider_openerp
msgid "Log in with Odoo.com"
msgstr "התחברות עם Odoo.com"

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_auth_oauth_provider__body
msgid "Login button label"
msgstr "תגית בכפתור כניסה"

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_res_users__oauth_access_token
msgid "OAuth Access Token"
msgstr ""

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_res_users__oauth_provider_id
msgid "OAuth Provider"
msgstr ""

#. module: auth_oauth
#: model:ir.ui.menu,name:auth_oauth.menu_oauth_providers
#: model_terms:ir.ui.view,arch_db:auth_oauth.res_config_settings_view_form
msgid "OAuth Providers"
msgstr ""

#. module: auth_oauth
#: model:ir.model.constraint,message:auth_oauth.constraint_res_users_uniq_users_oauth_provider_oauth_uid
msgid "OAuth UID must be unique per provider"
msgstr ""

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_res_users__oauth_uid
msgid "OAuth User ID"
msgstr ""

#. module: auth_oauth
#: model:ir.model,name:auth_oauth.model_auth_oauth_provider
msgid "OAuth2 provider"
msgstr ""

#. module: auth_oauth
#: model_terms:ir.ui.view,arch_db:auth_oauth.view_users_form
msgid "Oauth"
msgstr ""

#. module: auth_oauth
#: model:ir.model.fields,help:auth_oauth.field_res_users__oauth_uid
msgid "Oauth Provider user_id"
msgstr ""

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_auth_oauth_provider__name
msgid "Provider name"
msgstr "שם ספק"

#. module: auth_oauth
#: model:ir.actions.act_window,name:auth_oauth.action_oauth_provider
msgid "Providers"
msgstr "ספקים"

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_auth_oauth_provider__scope
msgid "Scope"
msgstr "תחום"

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_auth_oauth_provider__sequence
msgid "Sequence"
msgstr "רצף"

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_res_config_settings__server_uri_google
msgid "Server uri"
msgstr ""

#. module: auth_oauth
#. odoo-python
#: code:addons/auth_oauth/controllers/main.py:0
msgid "Sign up is not allowed on this database."
msgstr ""

#. module: auth_oauth
#: model:ir.model,name:auth_oauth.model_ir_config_parameter
msgid "System Parameter"
msgstr "פרמטר מערכת"

#. module: auth_oauth
#: model_terms:ir.ui.view,arch_db:auth_oauth.res_config_settings_view_form
msgid "Tutorial"
msgstr "מדריך"

#. module: auth_oauth
#: model:ir.model,name:auth_oauth.model_res_users
msgid "User"
msgstr "משתמש"

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_auth_oauth_provider__validation_endpoint
msgid "UserInfo URL"
msgstr ""

#. module: auth_oauth
#. odoo-python
#: code:addons/auth_oauth/controllers/main.py:0
msgid ""
"You do not have access to this database or your invitation has expired. "
"Please ask for an invitation and be sure to follow the link in your "
"invitation email."
msgstr ""

#. module: auth_oauth
#: model_terms:ir.ui.view,arch_db:auth_oauth.view_oauth_provider_form
#: model_terms:ir.ui.view,arch_db:auth_oauth.view_oauth_provider_tree
msgid "arch"
msgstr ""

#. module: auth_oauth
#: model_terms:ir.ui.view,arch_db:auth_oauth.res_config_settings_view_form
msgid "e.g. 1234-xyz.apps.googleusercontent.com"
msgstr ""
